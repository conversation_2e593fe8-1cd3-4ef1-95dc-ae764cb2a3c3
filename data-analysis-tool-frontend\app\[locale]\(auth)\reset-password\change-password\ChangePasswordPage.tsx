"use client";

import { showNotification } from "@/redux/slices/notificationSlice";
import axios from "@/lib/axios";
import { ArrowLeft, ShieldCheck, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useTranslations } from "next-intl";

const ChangePasswordPage = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
    watch,
  } = useForm();

  // Watch password fields to determine when to show the eye button
  const passwordValue = watch("password");
  const confirmPasswordValue = watch("confirmPassword");

  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const router = useRouter();
  const dispatch = useDispatch();
  const t = useTranslations();

  // Password visibility states
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/resetpassword`, {
        token,
        newPassword: data.password,
      });
      dispatch(
        showNotification({
          message: t('password_changed_successfully'),
          type: "success",
        })
      );
      router.push("/");
    } catch (error) {
      console.error(error);
    }
  };

  if (!token)
    return (
      <p className="text-red-500">
        {t('noResetToken')}
      </p>
    );

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            {t('createNewPassword')}
          </h1>
          <p className="text-neutral-700 text-center">
            {t('passwordRequirementsNote')}
          </p>
        </div>
        <form
          className="flex flex-col gap-4 "
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          <div className="label-input-group group">
            <label htmlFor="password" className="label-text">
              {t('newPassword')}
            </label>
            <div className="relative">
              <input
                {...register("password", {
                  required: t('pleaseEnterPassword'),
                  validate: {
                    minLength: (value) =>
                      value.length >= 8 ||
                      t('passwordMustBeAtLeast8Characters'),
                    hasUppercase: (value) =>
                      /[A-Z]/.test(value) ||
                      t('passwordMustContainAtLeastOneUppercaseLetter'),
                    hasNumber: (value) =>
                      /\d/.test(value) ||
                      t('passwordMustContainAtLeastOneNumber'),
                    hasSymbol: (value) =>
                      /[\W_]/.test(value) ||
                      t('passwordMustContainAtLeastOneSymbol'),
                  },
                })}
                id="password"
                type={showPassword ? "text" : "password"}
                className="input-field w-full pr-10"
                placeholder={t('enterNewPassword')}
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{`${errors.password.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="confirm-password" className="label-text">
              {t('confirmPassword')}
            </label>
            <div className="relative">
              <input
                {...register("confirmPassword", {
                  required: t('pleaseConfirmPassword'),
                  validate: (value) =>
                    value === getValues("password") || t('passwordsDoNotMatch'),
                })}
                id="confirm-password"
                type={showConfirmPassword ? "text" : "password"}
                className="input-field w-full pr-10"
                placeholder={t('confirmYourPassword')}
              />
              {confirmPasswordValue && confirmPasswordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">{`${errors.confirmPassword.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('updating')}
                <div className="animate-spin border-x-2 border-neutral-100 rounded-full size-4"></div>
              </span>
            ) : (
              <span className="flex items-center gap-2">{t('resetPassword')}</span>
            )}
          </button>
        </form>
        <Link
          href="/"
          className="text-neutral-700 self-center flex items-center gap-2"
        >
          <ArrowLeft size={16} /> {t('backToSignin')}
        </Link>
      </div>
    </div>
  );
};

export { ChangePasswordPage };
