import { showNotification } from "@/redux/slices/notificationSlice";
import axios from "@/lib/axios";
import { <PERSON>O<PERSON>, Eye } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useTranslations } from "next-intl";

const PasswordChange = () => {
  const t = useTranslations();
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000/api";
  const dispatch = useDispatch();

  // Initialize React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
      email: "",
    },
  });

  const watchNewPassword = watch("newPassword");
  const watchConfirmPassword = watch("confirmPassword");

  // Handle form submission
  const handleChangePassword = async (data: any) => {
    const { currentPassword, newPassword, confirmPassword } = data;

    // Client-side validation
    if (newPassword !== confirmPassword) {
      dispatch(
        showNotification({
          message: t('password_mismatch'),
          type: "error",
        })
      );
      return;
    }

    try {
      const response = await axios.post(`/users/changepassword`, {
        currentPassword,
        newPassword,
        confirmPassword,
      });

      if (response.status === 200) {
        dispatch(
          showNotification({
            message: t('password_changed_successfully'),
            type: "success",
          })
        );
        setIsChangingPassword(false);
        // Reset fields
        setValue("currentPassword", "");
        setValue("newPassword", "");
        setValue("confirmPassword", "");
      }
    } catch (error: any) {
      dispatch(
        showNotification({
          message: error.response?.data?.message || "Server error",
          type: "error",
        })
      );
    }
  };

  return (
    <div>
      {isChangingPassword ? (
        <form
          onSubmit={handleSubmit(handleChangePassword)}
          className="flex-col flex gap-4"
        >
          <div className="flex flex-col gap-2">
            {/* CURRENT PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="current-password" className="label-text">
                {t('current_password')}
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="current-password"
                  type={showCurrentPassword ? "text" : "password"}
                  placeholder= {t('enter_current_password')}
                  className="input-field w-full pr-10"
                  {...register("currentPassword", {
                    required: t('current_password_required'),
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showCurrentPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.currentPassword && (
                <p className="text-red-500 text-sm">
                  {errors.currentPassword.message}
                </p>
              )}
            </div>

            {/* NEW PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="new-password" className="label-text">
                {t('new_password')}
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="new-password"
                  type={showNewPassword ? "text" : "password"}
                  placeholder= {t('enter_new_password')}
                  className="input-field w-full pr-10"
                  {...register("newPassword", {
                    required: t('new_password_required'),
                    minLength: {
                      value: 6,
                      message: t('password_min_length'),
                    },
                    validate: (value) =>
                      value !== watch("currentPassword") || t('new_password_must_be_different'),
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showNewPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.newPassword && (
                <p className="text-red-500 text-sm">
                  {errors.newPassword.message}
                </p>
              )}
            </div>

            {/* CONFIRM PASSWORD */}
            <div className="label-input-group group">
              <label htmlFor="confirm-password" className="label-text">
                {t('confirm_password')}
              </label>
              <div className="relative laptop:w-1/3">
                <input
                  id="confirm-password"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder= {t('enter_confirm_password')}
                  className="input-field w-full pr-10"
                  {...register("confirmPassword", {
                    required: t('confirm_password_required'),
                    validate: (value) =>
                      value === watchNewPassword || t('passwords_do_not_match'),
                  })}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex gap-2">
            <button type="submit" className="btn-primary">
              {t('update_password')}
            </button>
            <button
              className="btn-outline"
              onClick={() => setIsChangingPassword(false)}
            >
              {t('cancel')}
            </button>
          </div>
        </form>
      ) : (
        <div className="flex items-center justify-between">
          <button
            className="btn-primary"
            onClick={() => setIsChangingPassword(true)}
          >
           {t('change_password')}
          </button>
        </div>
      )}
    </div>
  );
};

export { PasswordChange };

function dispatch(arg0: any) {
  throw new Error("Function not implemented.");
}
