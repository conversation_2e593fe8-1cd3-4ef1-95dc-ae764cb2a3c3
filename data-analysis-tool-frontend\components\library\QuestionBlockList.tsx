import { Question } from "@/types/formBuilder";
import { QuestionBlockListColumns } from "@/components/tables/columns/QuestionBlockListColumns";
import { QuestionListTable } from "../tables/QuestionListTable";
import { useState } from "react";

interface QuestionBlockListProps {
  questions: Question[];
}

export const QuestionBlockList = ({ questions }: QuestionBlockListProps) => {
  const [globalFilter, setGlobalFilter] = useState("");
  const columns = QuestionBlockListColumns();

  return (
    <div className="space-y-4">
      <QuestionListTable
        columns={columns}
        data={questions}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
      />
    </div>
  );
}; 