"use client";

import React, { use, useEffect, useState } from "react";
import { Shield, Mail, Lock, TrendingUp } from "lucide-react";
import { PasswordChange } from "@/components/password/PasswordChange";
import { DataTable } from "./data-table";
import { securityColumns } from "./columns";
import { useAuth } from "@/hooks/useAuth";
import { FieldValues, useForm } from "react-hook-form";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  changeEmail,
  fetchSessionInformations,
  sendVerificationEmail,
} from "@/lib/api/users";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import Spinner from "@/components/general/Spinner";
import { Session } from "@/types";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import { AxiosError } from "axios";
import { useTranslations } from "next-intl";

const page = () => {
  const [isChangingEmail, setIsChangingEmail] = useState(false);

  const { user, logout } = useAuth();
  const t = useTranslations();
  const columns = securityColumns()
  const {
    register,
    formState: { errors },
    handleSubmit,
    getValues,
    reset,
    setError,
  } = useForm();

  const {
    data: sessionsData,
    isLoading: sessionsLoading,
    isError: sessionsError,
  } = useQuery<Session[]>({
    queryKey: ["sessions", user?.id],
    queryFn: fetchSessionInformations,
    enabled: !!user?.id,
  });

  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const [
    showEmailChangeConfirmationModal,
    setShowEmailChangeConfirmationModal,
  ] = useState<boolean>(false);

  const profileMutation = useMutation({
    mutationFn: changeEmail,
    onSuccess: async () => {
      try {
        await queryClient.invalidateQueries({
          queryKey: ["profile", user?.id],
        });
        await sendVerificationEmail(getValues("email"));
        setIsChangingEmail(false);
        dispatch(
          showNotification({
            message:
              t('email_change_success'),
            type: "success",
          })
        );
        logout();
      } catch (err) {
        dispatch(
          showNotification({
            message:
              t('email_change_verificationFailed'),
            type: "warning",
          })
        );
      }
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        setError(error.response?.data.errorField, {
          message: error.response?.data.message,
        });
      } else {
        dispatch(
          showNotification({
            message: t('email_change_failed'),
            type: "error",
          })
        );
      }
    },
  });

  useEffect(() => {
    if (!isChangingEmail) {
      reset();
    }
  }, [isChangingEmail, reset]);

  const onConfirm = () => {
    handleSubmit(onSubmit)();
    setShowEmailChangeConfirmationModal(false);
  };

  // this prevents the page to show errors before completely mounting
  const [mounted, setMounted] = useState<boolean>(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const onSubmit = async (data: FieldValues) => {
    profileMutation.mutate({ email: data.email });
  };

  if (!mounted) return null;

  if (sessionsLoading) {
    return <Spinner />;
  }

  if (sessionsError || !sessionsData) {
    return <p className="text-sm text-red-500"> {t('error_loading_data')}</p>;
  }

  return (
    <>
      <ConfirmationModal
        showModal={showEmailChangeConfirmationModal}
        onClose={() => setShowEmailChangeConfirmationModal(false)}
        onConfirm={onConfirm}
        title= {t('email_change_confirm')}
        description= {t('email_change_warning')}
        confirmButtonText= {t('change')}
        confirmButtonClass="btn-primary"
      />
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Shield className="h-8 w-8" />
            <h2 className="heading-text">{t('security_settings')}</h2>
          </div>
          <p className="sub-text">
            {t('account_security_settings')}
          </p>
        </div>

        <div className="flex-col gap-10 flex">
          {/* PASSWORD SECTION */}
          <div className="flex flex-col gap-5 shadow-sm border-muted p-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                <h2 className="sub-heading-text">{t('password')}</h2>
              </div>
              <p className="sub-text">
                {t('account_update_password')}
              </p>
            </div>

            <div>
              <PasswordChange />
            </div>
          </div>

          {/* EMAIL SECTION */}
          <div className="flex flex-col gap-5 shadow-sm border-muted p-4">
            <div className="flex flex-col gap-2 ">
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                <h2 className="sub-heading-text">{t('email_address')}</h2>
              </div>
              <p className="sub-text">
                {t('account_email_usage')}
              </p>
            </div>

            <div>
              {isChangingEmail ? (
                <form
                  className="space-y-4"
                  noValidate
                  onSubmit={(e) => e.preventDefault()}
                >
                  <input
                    {...register("email", {
                      required: t('enter_new_email'),
                    })}
                    type="email"
                    placeholder="eg: <EMAIL>"
                    className="input-field"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">{`${errors.email?.message}`}</p>
                  )}
                  <div className="flex gap-2">
                    <button
                      type="button"
                      className="btn-primary"
                      onClick={() => setShowEmailChangeConfirmationModal(true)}
                    >
                      {t('save')}
                    </button>
                    <button
                      type="button"
                      className="btn-outline"
                      onClick={() => setIsChangingEmail(false)}
                    >
                      {t('cancel')}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="flex justify-between items-center ">
                  <span>{user?.email}</span>
                  <button
                    type="button"
                    className="btn-primary"
                    onClick={() => setIsChangingEmail(true)}
                  >
                    {t('change')}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <h2 className="heading-text">{t('recent_account_activity')} </h2>
          <DataTable columns={columns} data={sessionsData} />
        </div>
      </div>
    </>
  );
};

export default page;
