"use client";

import { Template } from "@/types";
import { GeneralTable } from "@/components/tables/GeneralTable";
import { TemplateList } from "@/components/library/TemplateList";
import { TemplateListColumns } from "@/components/tables/columns/TemplateListColumns";
import { QuestionBlockList } from "@/components/library/QuestionBlockList";
import { useQuestionBlockQuestions } from "@/hooks/useQuestionBlockQuestions";
import { use } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";

export default function LibraryPage() {
  // change this after creating question blocks
  const questionBlockData: Template[] = [];
  const { questions} = useQuestionBlockQuestions()
  const t = useTranslations();

  return (
    <div className="p-6 space-y-6 section gap-8">
      <h1 className="text-2xl font-semibold">{t('myLibrary')}</h1>
      <TemplateList />
      {/* change this after creating question blocks */}
      <div className="flex flex-col gap-4">
        <h1 className="sub-heading-text hover:text-neutral-700">
          <Link href={'/library/question-block/form-builder'}>
         {t('questionBlocks')}

          </Link>
          </h1>
        {questions.length > 0 ? (
          <QuestionBlockList questions={questions} />
        ) : (
          <div className="text-center py-16 space-y-4">
            <p className="text-lg">
              {t('getStarted')}
            </p>
            <p className="text-sm text-gray-500">
             {t('advancedUsers')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
