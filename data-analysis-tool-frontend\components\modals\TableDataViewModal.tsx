"use client";

import React from "react";
import Modal from "./Modal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableDataItem {
  column: string;
  row: string;
  value: string;
  columnId?: number;
  rowId?: number;
}

interface CellValue {
  columnId: number;
  rowsId: number;
  value: string;
}

interface TableColumn {
  id: number;
  columnName: string;
  parentColumnId?: number;
}

interface TableRowType {
  id: number;
  rowsName: string;
}

interface TableViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  tableData: TableDataItem[];
  uniqueColumns: string[];
  uniqueRows: string[];
  rowsMap: Map<string, Map<string, string>>;
  useParentChildColumns?: boolean;
  loading?: boolean;
  tableStructure?: {
    tableColumns?: TableColumn[];
    tableRows?: TableRowType[];
  };
}

const TableDataViewModal: React.FC<TableViewModalProps> = ({
  isOpen,
  onClose,
  title,
  tableData,
  uniqueColumns,
  uniqueRows,
  rowsMap,
  useParentChildColumns = false,
  loading = false,
  tableStructure,
}) => {
  // Parse the submitted table data to get the actual cell values
  const parsedTableData = React.useMemo(() => {
    if (!tableData || tableData.length === 0) return [];

    // Convert tableData to CellValue format
    const cellValues: CellValue[] = [];
    tableData.forEach((item) => {
      if (item.columnId !== undefined && item.rowId !== undefined) {
        cellValues.push({
          columnId: Number(item.columnId),
          rowsId: Number(item.rowId),
          value: item.value,
        });
      } else {
        const columnId = parseInt(item.column);
        const rowId = parseInt(item.row);

        if (!isNaN(columnId) && !isNaN(rowId)) {
          cellValues.push({
            columnId,
            rowsId: rowId,
            value: item.value,
          });
        }
      }
    });

    return cellValues;
  }, [tableData]);

  // Extract unique row IDs from the table data
  const uniqueRowIds = React.useMemo(() => {
    const rowIds = new Set<number>();
    tableData.forEach((item) => {
      if (item.rowId !== undefined) {
        rowIds.add(Number(item.rowId));
      } else if (item.row) {
        const rowId = parseInt(item.row);
        if (!isNaN(rowId)) {
          rowIds.add(rowId);
        }
      }
    });
    console.log("Extracted unique row IDs:", Array.from(rowIds));
    return Array.from(rowIds);
  }, [tableData]);

  // Ensure we have tableRows if they're missing but we have row data
  const enhancedTableStructure = React.useMemo(() => {
    if (!tableStructure) return null;

    const enhanced = JSON.parse(JSON.stringify(tableStructure));

    const needToCreateRows =
      !enhanced.tableRows || enhanced.tableRows.length === 0;
    const needToSupplementRows =
      enhanced.tableRows && enhanced.tableRows.length < uniqueRowIds.length;

    if (needToCreateRows) {
      console.log(
        "Creating tableRows from scratch using uniqueRowIds:",
        uniqueRowIds
      );

      enhanced.tableRows = uniqueRowIds.map((rowId) => {
        const matchingDataItem = tableData.find(
          (item) =>
            (item.rowId !== undefined && Number(item.rowId) === rowId) ||
            (item.row !== undefined && parseInt(item.row) === rowId)
        );

        const displayRowId = rowId < 0 ? Math.abs(rowId) : rowId;
        return {
          id: rowId,
          rowsName: matchingDataItem?.row
            ? rowId < 0
              ? String(Math.abs(rowId))
              : matchingDataItem.row
            : `Row ${displayRowId}`,
        };
      });

      console.log("Created tableRows:", enhanced.tableRows);
    } else if (needToSupplementRows) {
      console.log("Supplementing existing tableRows with missing rows");
      console.log("Existing rows:", enhanced.tableRows);

      const existingRowIds = new Set(
        enhanced.tableRows.map((row: any) => row.id)
      );
      const missingRowIds = uniqueRowIds.filter(
        (id) => !existingRowIds.has(id)
      );

      console.log("Missing row IDs:", missingRowIds);

      missingRowIds.forEach((rowId) => {
        const matchingDataItem = tableData.find(
          (item) =>
            (item.rowId !== undefined && Number(item.rowId) === rowId) ||
            (item.row !== undefined && parseInt(item.row) === rowId)
        );

        const displayRowId = rowId < 0 ? Math.abs(rowId) : rowId;
        enhanced.tableRows.push({
          id: rowId,
          rowsName: matchingDataItem?.row
            ? rowId < 0
              ? String(Math.abs(rowId))
              : matchingDataItem.row
            : `Row ${displayRowId}`,
        });
      });

      console.log("Updated tableRows:", enhanced.tableRows);
    }

    return enhanced;
  }, [tableStructure, tableData, uniqueRowIds, uniqueRows]);

  // Group columns by parent-child relationships
  const groupedColumns = React.useMemo(() => {
    if (
      !enhancedTableStructure?.tableColumns ||
      enhancedTableStructure.tableColumns.length === 0
    ) {
      return {
        parentColumns: [],
        columnMap: new Map<number, TableColumn[]>(),
        hasChildColumns: false,
      };
    }

    const parentColumns = enhancedTableStructure.tableColumns.filter(
      (col: TableColumn) =>
        col.parentColumnId === undefined || col.parentColumnId === null
    );

    const columnMap = new Map<number, TableColumn[]>();

    parentColumns.forEach((parentCol: TableColumn) => {
      const childColumns = enhancedTableStructure.tableColumns!.filter(
        (col: TableColumn) => col.parentColumnId === parentCol.id
      );
      columnMap.set(parentCol.id, childColumns);
    });

    const hasChildColumns = parentColumns.some(
      (p: TableColumn) => (columnMap.get(p.id) || []).length > 0
    );

    return { parentColumns, columnMap, hasChildColumns };
  }, [enhancedTableStructure]);

  // Create a map for quick cell value lookup
  const cellValueMap = React.useMemo(() => {
    const map = new Map<string, string>();
    parsedTableData.forEach((cell) => {
      map.set(`${cell.columnId}_${cell.rowsId}`, cell.value);
    });
    console.log(
      "Cell value map created with",
      parsedTableData.length,
      "entries"
    );
    console.log("Cell value map keys:", Array.from(map.keys()));
    return map;
  }, [parsedTableData]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="p-6 rounded-md max-w-4xl w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%]"
    >
      <div className="flex flex-col gap-4">
        <h2 className="text-xl font-semibold text-neutral-700">{title}</h2>

        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
            <span className="ml-2 text-neutral-600">Loading table data...</span>
          </div>
        ) : !enhancedTableStructure?.tableColumns ||
          enhancedTableStructure.tableColumns.length === 0 ? (
          <div className="py-4 text-center text-amber-600">
            <p>No table structure available.</p>
            <p className="text-sm mt-2">Debug info:</p>
            <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(
                {
                  hasTableStructure: !!enhancedTableStructure,
                  tableColumnsLength:
                    enhancedTableStructure?.tableColumns?.length || 0,
                  tableRowsLength:
                    enhancedTableStructure?.tableRows?.length || 0,
                  tableDataLength: tableData?.length || 0,
                  useParentChildColumns,
                },
                null,
                2
              )}
            </pre>
          </div>
        ) : (
          <div className="overflow-auto max-h-[70vh]">
            <Table className="border-collapse border border-amber-700">
              <TableHeader className="bg-amber-100">
                <TableRow>
                  <TableHead
                    className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-100"
                    rowSpan={groupedColumns.hasChildColumns ? 2 : 1}
                  >
                    S.No.
                  </TableHead>
                  {groupedColumns.parentColumns.map(
                    (parentCol: TableColumn) => {
                      const childColumns =
                        groupedColumns.columnMap.get(parentCol.id) || [];
                      const colSpan = childColumns.length || 1;

                      return (
                        <TableHead
                          key={parentCol.id}
                          colSpan={colSpan}
                          rowSpan={childColumns.length === 0 ? 2 : 1}
                          className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider text-center border border-amber-700 bg-amber-100"
                        >
                          {parentCol.columnName}
                        </TableHead>
                      );
                    }
                  )}
                </TableRow>
                {groupedColumns.hasChildColumns && (
                  <TableRow>
                    {groupedColumns.parentColumns.map(
                      (parentCol: TableColumn) => {
                        const childColumns =
                          groupedColumns.columnMap.get(parentCol.id) || [];

                        if (childColumns.length === 0) {
                          return null;
                        }

                        return childColumns.map((childCol: TableColumn) => (
                          <TableHead
                            key={childCol.id}
                            className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider border border-amber-700 bg-amber-50"
                          >
                            {childCol.columnName}
                          </TableHead>
                        ));
                      }
                    )}
                  </TableRow>
                )}
              </TableHeader>
              <TableBody>
                {(() => {
                  console.log(
                    "Table rows to render:",
                    enhancedTableStructure?.tableRows || []
                  );
                  return null;
                })()}
                {enhancedTableStructure?.tableRows?.length === 0 && (
                  <TableRow>
                    <TableCell
                      colSpan={groupedColumns.parentColumns.length + 1}
                      className="text-center py-4"
                    >
                      No rows found in table structure
                    </TableCell>
                  </TableRow>
                )}
                {enhancedTableStructure?.tableRows?.map(
                  (row: TableRowType, rowIndex: number) => (
                    <TableRow key={row.id} className="bg-white">
                      <TableCell className="px-3 py-2 text-xs font-medium border border-amber-700 bg-amber-50 text-center">
                        {rowIndex + 1}
                      </TableCell>
                      {groupedColumns.parentColumns.map(
                        (parentCol: TableColumn) => {
                          const childColumns =
                            groupedColumns.columnMap.get(parentCol.id) || [];

                          if (childColumns.length === 0) {
                            return (
                              <TableCell
                                key={`cell-${parentCol.id}-${row.id}`}
                                className="px-3 py-2 text-xs border border-amber-700"
                                colSpan={1}
                              >
                                {cellValueMap.get(
                                  `${parentCol.id}_${row.id}`
                                ) || ""}
                              </TableCell>
                            );
                          }

                          return childColumns.map((childCol) => (
                            <TableCell
                              key={`cell-${childCol.id}-${row.id}`}
                              className="px-3 py-2 text-xs border border-amber-700"
                            >
                              {cellValueMap.get(`${childCol.id}_${row.id}`) ||
                                ""}
                            </TableCell>
                          ));
                        }
                      )}
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
          </div>
        )}
        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default TableDataViewModal;
