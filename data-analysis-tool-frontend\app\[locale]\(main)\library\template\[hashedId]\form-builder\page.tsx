"use client";

import { FormPreview } from "@/components/form-preview";
import { useState } from "react";
import { Question } from "@/types/formBuilder";
import { useQuery } from "@tanstack/react-query";
import { fetchTemplateQuestions } from "@/lib/api/form-builder";
import Spinner from "@/components/general/Spinner";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { FormBuilder } from "@/components/form-builder/FormBuilder";
import { useTranslations } from "next-intl";

const permissions = {
  viewForm: true,
  editForm: true,
  viewSubmissions: true,
  addSubmissions: true,
  deleteSubmissions: true,
  editSubmissions: true,
  manageProject: true,
  validateSubmissions: true,
};

export default function FormBuilderPage() {
  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;

  const templateId = decode(hashedIdString);
  const t = useTranslations();

  const {
    data: questionsData,
    isLoading: questionsLoading,
    isError: questionsError,
  } = useQuery<Question[]>({
    queryKey: ["templateQuestions", templateId],
    queryFn: () => fetchTemplateQuestions({ templateId: templateId! }),
    enabled: !!templateId,
  });

  if (!hashedId || templateId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('errorInvalidProjectId')}</h1>
        <p className="text-neutral-700">
         {t('invalidUrlProjectId')}
        </p>
      </div>
    );
  }

  if (questionsLoading || !questionsData) {
    return <Spinner />;
  }
  if (questionsError) {
    return (
      <p className="text-sm text-red-500">
        {t('errorLoadingForm')}
      </p>
    );
  }

  return (
    <div className="p-6">
      {isPreviewMode ? (
        <FormPreview
          questions={questionsData}
          questionGroups={[]}
          contextType="template"
          onClose={() => setIsPreviewMode(false)}
          hashedId={hashedIdString} // Pass hashedId
        />
      ) : (
        <FormBuilder
          setIsPreviewMode={setIsPreviewMode}
          questions={questionsData}
          contextType="template"
          contextId={templateId}
          permissions={permissions}
        />
      )}
    </div>
  );
}
