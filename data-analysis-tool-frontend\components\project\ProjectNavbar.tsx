"use client";

import { PanelsTopLeft, Settings, FilePen, Database } from "lucide-react";
import { useParams } from "next/navigation";
import { Navbar } from "@/components/general/SecondaryNavbar";
import { ProjectPermissionFlags } from "@/types";
import { useLocale } from "next-intl";
import { useTranslations } from "next-intl";

const ProjectNavbar = ({
  permissions,
}: {
  permissions: ProjectPermissionFlags;
}) => {
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const t = useTranslations();
  const locale = useLocale();
  const isOwnerOrManager = permissions.manageProject;

  const canAccessFormBuilder =
    isOwnerOrManager || permissions.viewForm || permissions.editForm;
  const canAccessData =
    isOwnerOrManager ||
    permissions.viewSubmissions ||
    permissions.editSubmissions ||
    permissions.addSubmissions ||
    permissions.deleteSubmissions;
  const canAccessSettings = isOwnerOrManager;

  const projectNavbarItems = [
    {
      label: t('overview'),
      icon: <PanelsTopLeft size={16} />,
      route: `/${locale}/project/${hashedIdString}/overview`,
      disabled: false,
    },
    {
      label: t('formBuilder'),
      icon: <FilePen size={16} />,
      route: `/${locale}/project/${hashedIdString}/form-builder`,
      disabled: !canAccessFormBuilder,
    },
    {
      label: t('data'),
      icon: <Database size={16} />,
      route: `/${locale}/project/${hashedIdString}/data`,
      disabled: !canAccessData,
    },
    {
      label: t('settings'),
      icon: <Settings size={16} />,
      route: `/${locale}/project/${hashedIdString}/settings`,
      disabled: !canAccessSettings,
    },
  ];

  return <Navbar items={projectNavbarItems} />;
};

export { ProjectNavbar };
