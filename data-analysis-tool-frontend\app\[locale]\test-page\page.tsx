'use client';

import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function TestPage() {
  const t = useTranslations();
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Internationalization Test Page</h1>
      
      <div className="mb-4">
        <p>Current translations:</p>
        <ul className="list-disc pl-5">
          <li>Save: {t('save')}</li>
          <li>Cancel: {t('cancel')}</li>
        </ul>
      </div>
      
      <div className="flex gap-4">
        <Link href="/en/test-page" className="px-4 py-2 bg-blue-500 text-white rounded">
          English
        </Link>
        <Link href="/ne/test-page" className="px-4 py-2 bg-blue-500 text-white rounded">
          Nepali
        </Link>
      </div>
      
      <div className="mt-4">
        <Link href="/" className="text-blue-500 underline">
          Go to Home
        </Link>
      </div>
    </div>
  );
}