"use client";

import Spinner from "@/components/general/Spinner";
import { ProjectNavbar } from "@/components/project/ProjectNavbar";
import { useAuth } from "@/hooks/useAuth";
import { useProjectPermissions } from "@/hooks/useProjectPermissions";
import { fetchProjectById } from "@/lib/api/projects";
import { decode } from "@/lib/encodeDecode";
import { Project } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useMemo } from "react";
import { useTranslations } from "next-intl";

const Layout = ({ children }: { children: React.ReactNode }) => {
  const { hashedId } = useParams();
  const { user } = useAuth();

  const hashedIdString = hashedId as string;
  const t = useTranslations();

  const projectId = useMemo(() => decode(hashedIdString), [hashedIdString]);

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id,
  });

  const permissions = useProjectPermissions({
    projectData,
    user,
  });
  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('invalidProjectIdError')}</h1>
        <p className="text-neutral-700">
          {t('invalidProjectIdMessage')}
        </p>
      </div>
    );
  }

  if (projectLoading) {
    return <Spinner />;
  }

  if (projectError) {
    return (
      <p className="text-red-500">{t('fetchProjectFailed')}</p>
    );
  }

  return (
    <div className="section flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="heading-text capitalize">{projectData?.name}</h1>
        <Link href="/dashboard" className="flex items-center gap-2">
          <ArrowLeft size={16} />
          {t('backToDashboard')}
        </Link>
      </div>
      <ProjectNavbar permissions={permissions} />
      <div className="px-8">{children}</div>
    </div>
  );
};

export default Layout;
