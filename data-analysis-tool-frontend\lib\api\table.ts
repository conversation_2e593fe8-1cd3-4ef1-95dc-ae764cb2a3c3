import axios from "@/lib/axios";

export interface TableColumn {
  id: number;
  columnName: string;
  parentColumnId?: number;
  childColumns?: TableColumn[];
}

export interface DefaultValue {
  columnId: number;
  value: string;
  code?: string;
}

export interface TableRow {
  id: number;
  rowsName: string;
  defaultValues?: DefaultValue[];
}

export interface CellValue {
  columnId: number;
  rowsId: number;
  value: string;
  code?: string;
  isDefault?: boolean;
}

export interface TableQuestion {
  id: number;
  label: string;
  inputType: string;
  tableColumns: TableColumn[];
  tableRows: TableRow[];
}

// Fetch table structure (columns and rows)
export const fetchTableStructure = async (questionId: number) => {
  try {
    if (!questionId || isNaN(questionId)) {
      console.error("Invalid questionId:", questionId);
      throw new Error("Invalid question ID provided");
    }

    // First try the table-questions endpoint
    try {
      const response = await axios.get(`/table-questions/${questionId}`);

      // Check if the response has the expected structure
      if (response.data && response.data.data && response.data.data.question) {
<<<<<<< HEAD
        console.log("Using question from data.data.question");
        // Include both question and cellValues in the response
        const tableData = response.data.data.question;
        const cellValues = response.data.data.cellValues || {};

        // Merge cellValues into the table data for backward compatibility
        return {
          ...tableData,
          cellValues: cellValues,
        };
=======
        return response.data.data.question;
>>>>>>> dfd4e3d973241b7e3694586368b52a1e0a4b1ec7
      } else if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.success) {
        return response.data;
      }
    } catch (err) {
      console.error("Error from /table-questions/ endpoint:", err);
      // Continue to try the next endpoint
    }

    // If that fails, try the questions endpoint
    try {
      const response = await axios.get(`/questions/${questionId}`);

      if (response.data && response.data.data) {
        return response.data.data;
      }
    } catch (err) {
      console.error("Error from /questions/ endpoint:", err);
      // Continue to try the next endpoint
    }

    // If that fails, try the tables endpoint as a last resort
    try {
      const response = await axios.get(`/tables/${questionId}`);

      if (response.data && response.data.data && response.data.data.question) {
        return response.data.data.question;
      }
    } catch (err) {
      console.error("Error from /tables/ endpoint:", err);
    }

    // If all endpoints fail, throw an error
    console.error("All endpoints failed to return valid data");
    throw new Error("Failed to fetch table structure from any endpoint");
  } catch (error) {
    console.error("Error fetching table structure:", error);
    throw error;
  }
};

// Save cell values
export const saveCellValues = async (
  questionId: number,
  cellValues: CellValue[]
) => {
  try {
    const { data } = await axios.post(`/table-questions/cells`, {
      questionId,
      cellValues,
    });
    return data.data;
  } catch (error) {
    console.error("Error saving cell values:", error);
    throw error;
  }
};

// Create a new table
// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices
// (1-based) that reference the position of the parent column in the array.
// For example, if column B is a child of column A, and column A is the first column in the array,
// then column B's parentColumnId should be 1.
// This is different from updateTable, which uses actual database IDs.
export const createTable = async (
  label: string,
  projectId: number,
  columns: { columnName: string; parentColumnId?: number }[],
  rows?: { rowsName: string; defaultValues?: DefaultValue[] }[]
) => {
  try {
    // Validate inputs before sending to API
    if (!label || !label.trim()) {
      throw new Error("Table label is required");
    }

    if (!projectId || isNaN(projectId)) {
      throw new Error("Valid project ID is required");
    }

    if (!columns || !Array.isArray(columns) || columns.length === 0) {
      throw new Error("At least one column is required");
    }

    // Rows are now optional - validate only if provided
    if (rows && !Array.isArray(rows)) {
      throw new Error("Rows must be an array if provided");
    }

    // Ensure all columns have valid names
    const invalidColumns = columns.filter(
      (col) => !col.columnName || !col.columnName.trim()
    );
    if (invalidColumns.length > 0) {
      throw new Error("All columns must have valid names");
    }

    // Ensure all rows have valid names if rows are provided
    if (rows) {
      const invalidRows = rows.filter(
        (row) => !row.rowsName || !row.rowsName.trim()
      );
      if (invalidRows.length > 0) {
        throw new Error("All rows must have valid names");
      }
    }

    // The columns are already ordered correctly with parent-child relationships
    // We just need to pass them through to the backend

    // Create a clean version of the columns to send to the backend
    const cleanedColumns: {
      columnName: string;
      parentColumnId?: number;
    }[] = columns.map((col) => ({
      columnName: col.columnName,
      parentColumnId: col.parentColumnId,
    }));

    // Log the columns being sent to the backend

    // Log the rearranged columns
<<<<<<< HEAD
    console.log("Rearranged columns for backend:", cleanedColumns);

    // Ensure rows is always an array, even if empty
    const processedRows = rows || [];

    // Process rows to ensure default values are properly formatted
    const cleanedRows = processedRows.map((row) => {
      const cleanedRow: {
        rowsName: string;
        defaultValues: DefaultValue[];
      } = {
        rowsName: row.rowsName.trim(),
        defaultValues: [],
      };

      // Process default values if they exist
      if (row.defaultValues && row.defaultValues.length > 0) {
        console.log(
          `Processing ${row.defaultValues.length} default values for row ${row.rowsName}:`,
          row.defaultValues
        );

        // Filter out invalid default values but keep all valid ones
        cleanedRow.defaultValues = row.defaultValues
          .filter((dv) => {
            const isValid =
              dv.columnId &&
              typeof dv.columnId === "number" &&
              dv.columnId > 0 &&
              dv.value !== undefined &&
              dv.value !== null &&
              String(dv.value).trim() !== "";

            if (!isValid) {
              console.warn(`Filtering out invalid default value:`, dv);
            }
            return isValid;
          })
          .map((dv) => {
            const mappedValue = {
              columnId: dv.columnId,
              value: String(dv.value).trim(),
              code: dv.code || String(dv.value).trim(),
            };
            console.log(`Mapped default value:`, mappedValue);
            return mappedValue;
          });
      }

      console.log(
        `Row ${row.rowsName} has ${cleanedRow.defaultValues.length} default values after cleaning:`,
        cleanedRow.defaultValues
      );
      return cleanedRow;
    });

    console.log("Creating table with data:", {
      label,
      projectId,
      columns: cleanedColumns,
      rows: cleanedRows,
    });

    // Log the exact structure of rows with default values for debugging
    console.log("Rows with default values:");
    cleanedRows.forEach((row, index) => {
      console.log(`Row ${index + 1} (${row.rowsName}):`);
      console.log(`  Default values: ${row.defaultValues.length}`);
      row.defaultValues.forEach((dv, i) => {
        console.log(
          `    ${i + 1}. columnId: ${dv.columnId}, value: "${
            dv.value
          }", code: "${dv.code || dv.value}"`
        );
      });
    });
=======
>>>>>>> dfd4e3d973241b7e3694586368b52a1e0a4b1ec7

    // Use the table-questions endpoint which creates both a question and table structure
    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here
    const { data } = await axios.post(`/table-questions`, {
      label,
      projectId,
      columns: cleanedColumns,
      rows: cleanedRows, // Use the cleaned rows
    });

    if (!data || !data.success) {
      throw new Error(data?.message || "Failed to create table");
    }

    return data.data;
  } catch (error: any) {
    console.error("Error creating table:", error);

    // Enhance error message with response details if available
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);

      // If we have a more specific error message from the server, use it
      if (error.response.data && error.response.data.message) {
        error.message = error.response.data.message;
      }
    }

    throw error;
  }
};

// Delete a table
export const deleteTable = async (tableId: number) => {
  try {
    const { data } = await axios.delete(`/table-questions/${tableId}`);
    return data;
  } catch (error) {
    console.error("Error deleting table:", error);
    throw error;
  }
};

// Update an existing table
// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.
// For new columns (without an ID), use the position (1-based index) of the parent column in the array.
// For example:
// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.
// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.
export const updateTable = async (
  tableId: number,
  label: string,
  columns: { id?: number; columnName: string; parentColumnId?: number }[],
  rows?: { id?: number; rowsName: string; defaultValues?: DefaultValue[] }[]
) => {
  try {
    // Validate inputs before sending to API
    if (!label || !label.trim()) {
      throw new Error("Table label is required");
    }

    if (!tableId || isNaN(tableId)) {
      throw new Error("Valid table ID is required");
    }

    if (!columns || !Array.isArray(columns) || columns.length === 0) {
      throw new Error("At least one column is required");
    }

    // Rows are now optional - validate only if provided
    if (rows && !Array.isArray(rows)) {
      throw new Error("Rows must be an array if provided");
    }

    // Ensure all columns have valid names
    const invalidColumns = columns.filter(
      (col) => !col.columnName || !col.columnName.trim()
    );
    if (invalidColumns.length > 0) {
      throw new Error("All columns must have valid names");
    }

    // Ensure all rows have valid names if rows are provided
    if (rows) {
      const invalidRows = rows.filter(
        (row) => !row.rowsName || !row.rowsName.trim()
      );
      if (invalidRows.length > 0) {
        throw new Error("All rows must have valid names");
      }
    }

    // Validate parent-child relationships
    // Check for circular references or invalid parent IDs
    const columnIdMap = new Map();
    const columnPositionMap = new Map();

    // Map columns by ID and position
    columns.forEach((col, index) => {
      if (col.id) {
        columnIdMap.set(col.id, col);
      }
      // Store 1-based position
      columnPositionMap.set(index + 1, col);
    });

    // Check each column with a parent
    for (const col of columns) {
      if (col.parentColumnId) {
        // Ensure parentColumnId is a positive number
        if (col.parentColumnId <= 0) {
          throw new Error(
            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`
          );
        }

        // Try to find parent by ID first
        let parentCol = columns.find((c) => c.id === col.parentColumnId);

        // If not found by ID, try to find by position (for new columns)
        if (!parentCol && col.parentColumnId <= columns.length) {
          parentCol = columnPositionMap.get(col.parentColumnId);
        }

        // If we still can't find the parent, it's an error
        if (!parentCol) {
          throw new Error(
            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`
          );
        }

        // Check for circular references
        // If this column has a parent, and that parent also has a parent,
        // it would create a 3rd level, which we don't support
        if (parentCol.parentColumnId) {
          throw new Error(
            "Cannot create more than 2 levels of nested columns (parent → child → grandchild)"
          );
        }
      }
    }

    // The columns are already ordered correctly with parent-child relationships
    // We just need to pass them through to the backend

    // Create a clean version of the columns to send to the backend
    const cleanedColumns: {
      id?: number;
      columnName: string;
      parentColumnId?: number;
    }[] = columns.map((col) => {
      const cleanCol: {
        id?: number;
        columnName: string;
        parentColumnId?: number;
      } = {
        columnName: col.columnName.trim(),
      };

      if (col.id) {
        cleanCol.id = col.id;
      }

      if (col.parentColumnId !== undefined) {
        cleanCol.parentColumnId = col.parentColumnId;
      }

      return cleanCol;
    });

    // Log the columns being sent to the backend

    // Use the table-questions endpoint to update the table
    try {
      const { data } = await axios.patch(`/table-questions/${tableId}`, {
        label: label.trim(),
        columns: cleanedColumns,
        rows: rows
          ? rows.map((row) => ({
              ...row,
              rowsName: row.rowsName.trim(),
            }))
          : [],
      });

      if (!data || !data.success) {
        throw new Error(data?.message || "Failed to update table");
      }

      return data.data;
    } catch (apiError: any) {
      console.error("API error updating table:", apiError);

      // Enhance error message with response details if available
      if (apiError.response) {
        console.error("Response status:", apiError.response.status);
        console.error("Response data:", apiError.response.data);

        // If we have a more specific error message from the server, use it
        if (apiError.response.data && apiError.response.data.message) {
          throw new Error(apiError.response.data.message);
        }
      }

      // If we don't have a specific error message, throw the original error
      throw apiError;
    }
  } catch (error: any) {
    console.error("Error updating table:", error);

    // Rethrow the error with a clear message
    if (error.message) {
      throw new Error(`Failed to update table: ${error.message}`);
    } else {
      throw new Error("Failed to update table due to an unknown error");
    }
  }
};
