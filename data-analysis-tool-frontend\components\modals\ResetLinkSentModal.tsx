import React, { use } from "react";
import Modal from "./Modal";
import { ArrowLeft, Mail, ShieldCheck } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
const ResetLinkSentModal = ({
  email,
  showModal,
  setShowModal,
}: {
  email: string;
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const t = useTranslations();
  const steps = [
    t('checkEmailSteps1'),
    t('checkEmailSteps2'),
    t('checkEmailSteps3'),
    t('checkEmailSteps4'),
  ];

  return (
    <Modal
      isOpen={showModal}
      onClose={() => setShowModal(false)}
      className="flex flex-col gap-8"
    >
      <div className="flex flex-col items-center gap-2">
        <ShieldCheck size={36} />
        <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
          {t('checkYourEmail')}
        </h1>
        <p className="text-neutral-700 text-center">
          {t('resetLinkSentTo')} {email}
        </p>
      </div>
      <div className="rounded-md p-4 bg-neutral-200 text-neutral-700 flex flex-col gap-2">
        <span className="flex items-center gap-2 text-lg font-medium">
          <Mail size={18} /> {t('whatToDoNext')}
        </span>
        <ol>
          {steps.map((step, index) => (
            <li key={index}>{`${index + 1}. ${step}`}</li>
          ))}
        </ol>
      </div>
      <Link
        href="/"
        className="text-neutral-700 self-center flex items-center gap-2"
      >
        <ArrowLeft size={16} /> {t('backToSignin')}
      </Link>
    </Modal>
  );
};

export { ResetLinkSentModal };
