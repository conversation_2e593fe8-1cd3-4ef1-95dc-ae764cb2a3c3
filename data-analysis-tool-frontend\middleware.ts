// middleware.ts
import { NextRequest, NextResponse } from "next/server";
import createIntlMiddleware from "next-intl/middleware";

// Create the internationalization middleware with improved configuration
const intlMiddleware = createIntlMiddleware({
  locales: ["en", "ne"],
  defaultLocale: "en",
  localePrefix: "always",
  // Enable locale detection from headers and cookies
  localeDetection: true,
  // Add alternate links for SEO
  alternateLinks: true,
});

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static assets and API routes
  const shouldSkipMiddleware =
    [
      "/favicon.ico",
      "/images",
      "/fonts",
      "/api",
      "/_next",
      "/edit-submission", // Skip auth for edit-submission routes
      "/form-submission", // Skip auth for form-test routes
    ].some((path) => pathname.startsWith(path)) ||
    pathname.match(/\.(jpg|jpeg|png|gif|svg|css|js|ico|woff|woff2|ttf|eot)$/);

  if (shouldSkipMiddleware) {
    return NextResponse.next();
  }

  // Handle internationalization first
  const intlResponse = intlMiddleware(request);

  // If intl middleware returns a redirect (e.g., adding locale prefix), return it immediately
  if (intlResponse.status !== 200) {
    return intlResponse;
  }

  // Extract locale from the pathname more reliably
  const localeCookie = request.cookies.get("NEXT_LOCALE")?.value;
  const localeMatch = pathname.match(/^\/(en|ne)(?:\/|$)/);
  const locale = localeMatch ? localeMatch[1] : (localeCookie || "en");

  // --- Locale Cookie Sync Logic ---
  if (localeCookie !== locale) {
    // Set the cookie, but do NOT redirect—just continue
    const response = intlResponse;
    response.cookies.set("NEXT_LOCALE", locale, { path: "/" });
    return response;
  }
  // --- End Locale Cookie Sync Logic ---

  // Extract the path without locale prefix for auth checks
  const pathWithoutLocale = pathname.replace(/^\/(en|ne)/, "") || "/";

  // Get token from cookies
  const token = request.cookies.get("token")?.value;

  // Define auth pages (pages that don't require authentication)
  const authPages = [
    "/",
    "/signup",
    "/reset-password",
    "/reset-password/change-password",
  ];

  const isAuthPage = authPages.includes(pathWithoutLocale);

  // Special case for form-test sign-in - allow access regardless of auth status
  if (pathname.startsWith("/form-submission") && pathname.includes("/sign-in")) {
    return intlResponse; // Return the intl response to maintain locale handling
  }

  // Validate token
  let isValidToken = false;

  if (token) {
    try {
      // Parse JWT token to check expiry
      const tokenData = JSON.parse(atob(token.split(".")[1]));
      const expiry = tokenData.exp * 1000; // Convert to milliseconds
      isValidToken = Date.now() < expiry;
    } catch (error) {
      // If token parsing fails, consider it invalid
      console.warn("Invalid token format:", error);
      isValidToken = false;
    }
  }

  // Skip auth checks for special routes but maintain locale handling
  if (
    pathWithoutLocale.startsWith("/form-submission") ||
    pathWithoutLocale.startsWith("/edit-submission") ||
    pathWithoutLocale.startsWith("/test-page")
  ) {
    return intlResponse;
  }

  // Redirect authenticated users away from auth pages
  if (isValidToken && isAuthPage) {
    const url = request.nextUrl.clone();
    url.pathname = `/${locale}/dashboard`;
    return NextResponse.redirect(url);
  }

  // Redirect unauthenticated users to login page
  if (!isValidToken && !isAuthPage) {
    const url = request.nextUrl.clone();
    url.pathname = `/${locale}/`;
    return NextResponse.redirect(url);
  }

  // All checks passed, continue with the intl response
  return intlResponse;
}

export const config = {
  // Match all paths except static assets and API routes
  matcher: [
    // Include all paths except static files and API routes
    "/((?!_next/static|_next/image|favicon.ico|images|fonts|api).*)",
  ],
};
