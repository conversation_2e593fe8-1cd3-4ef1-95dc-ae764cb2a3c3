"use client";

import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { IoChevronForward } from "react-icons/io5";
import { useQuery } from "@tanstack/react-query";
import { fetchTemplates } from "@/lib/api/templates";
import { useAuth } from "@/hooks/useAuth";
import Spinner from "../general/Spinner";
import { GeneralTable } from "../tables/GeneralTable";
import { Template } from "@/types";
import { getTemplateListColumnsForProjectCreation } from "../tables/columns/TemplateListColumnsForProjectCreation";
import { CreateProjectFromTemplateModal } from "./CreateProjectFromTemplateModal";
import { useTranslations } from "next-intl";

type ProjectTemplateModalProps = {
  isOpen: boolean;
  onClose: () => void;
  back: () => void;
};

const ChooseTemplateModal: React.FC<ProjectTemplateModalProps> = ({
  isOpen,
  back,
  onClose,
}) => {
  const { user } = useAuth();

  const {
    data: templatesData,
    isLoading: templatesLoading,
    isError: templatesError,
  } = useQuery<Template[]>({
    queryFn: fetchTemplates,
    queryKey: ["templates", user?.id],
    enabled: !!user?.id,
  });

  const [selectedRowId, setSelectedRowId] = useState<number | null>(null);
  const [showCreateProjectModal, setShowCreateProjectModal] =
    useState<boolean>(false);
  const [isExiting, setIsExiting] = useState<boolean>(false);
  const t = useTranslations();

  const columns = getTemplateListColumnsForProjectCreation({
    selectedRowId,
    setSelectedRowId,
  });

  const handleNext = () => {
    if (!selectedRowId) return;
    setShowCreateProjectModal(true);
  };

  const handleCloseCreateProjectModal = (action: "back" | "close") => {
    setIsExiting(true);
    // Allow time for exit animation before actually changing state
    setTimeout(() => {
      setShowCreateProjectModal(false);
      setSelectedRowId(null);
      if (action === "close") {
        onClose();
      }

      setIsExiting(false);
    }, 300); // Match this to the exit animation duration in Modal component
  };

  if (!templatesData) return null;

  if (templatesLoading) {
    return <Spinner />;
  }
  if (templatesError) {
    return <div>{t('error_loading_data')}</div>;
  }
  return (
    <>
      <Modal
        isOpen={isOpen && !showCreateProjectModal}
        onClose={() => {
          setSelectedRowId(null);
          onClose();
        }}
        className="bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-4xl mx-auto"
      >
        <div className="flex flex-col gap-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-neutral-700">
              {t('selectTemplate')}
            </h2>
          </div>
          <GeneralTable data={templatesData} columns={columns} />

          <div className="flex justify-end gap-3 mt-4">
            <button
              onClick={() => {
                setSelectedRowId(null);
                back();
              }}
              className="btn-outline"
            >
              {t('back')}
            </button>
            <button
              type="button"
              disabled={!selectedRowId}
              className="btn-primary"
              onClick={handleNext}
            >
              {t('next')}
              <IoChevronForward />
            </button>
          </div>
        </div>
      </Modal>
      {(showCreateProjectModal || isExiting) && selectedRowId !== null && (
        <CreateProjectFromTemplateModal
          showModal={showCreateProjectModal && !isExiting}
          closeModal={() => handleCloseCreateProjectModal("close")}
          back={() => handleCloseCreateProjectModal("back")}
          templateId={selectedRowId}
        />
      )}
    </>
  );
};

export { ChooseTemplateModal };
