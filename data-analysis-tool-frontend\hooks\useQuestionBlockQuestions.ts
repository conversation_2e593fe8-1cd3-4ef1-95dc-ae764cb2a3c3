import { useQuery } from "@tanstack/react-query";
import { fetchQuestionBlockQuestions } from "@/lib/api/form-builder";
import { Question } from "@/types/formBuilder";
import { useAuth } from "./useAuth";

export const useQuestionBlockQuestions = () => {
  const { user } = useAuth();
  const userId = user?.id;

  const {
    data: questionsData,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery<Question[]>({
    queryKey: ["questionBlockQuestions", userId],
    queryFn: () => fetchQuestionBlockQuestions(),
    enabled: !!userId,
    retry: 1
  });

  // Ensure questionsData is always an array
  const safeQuestionsData = Array.isArray(questionsData) ? questionsData : [];

  return {
    questions: safeQuestionsData,
    isLoading,
    isError,
    error,
    refetch,
    userId
  };
};
