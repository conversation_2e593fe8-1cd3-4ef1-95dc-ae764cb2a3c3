"use client";

import { JS<PERSON>, useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import axios from "@/lib/axios";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Project } from "@/types";
import { deployProject, fetchProjectById } from "@/lib/api/projects";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { Select } from "@/components/general/Select";
import { Globe, Briefcase, FileText } from "lucide-react";
import Spinner from "@/components/general/Spinner";
import { SectorLabelMap } from "@/constants/sectors";
import countries from "@/constants/countryNames.json";
import { labelToKey } from "@/lib/labelToKey";
import { useAuth } from "@/hooks/useAuth";
import { ShareProjectModal } from "@/components/modals/ShareProjectModal";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import { deleteProject } from "@/lib/api/projects";
import { archiveProject } from "@/lib/api/projects";
import { useTranslations } from "next-intl";
// import { addProjectUser } from "@/lib/api/projects";

const updateProject = async ({
  projectId,
  dataToSend,
}: {
  projectId: number;
  dataToSend: {
    name: string;
    description: string;
    sector: string;
    country: string;
  };
}) => {
  const { data } = await axios.patch(`/projects/${projectId}`, dataToSend);
  return data;
};

const ProjectSettingsPage = () => {
  const [hasMounted, setHasMounted] = useState<boolean>(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
    reset,
  } = useForm();

  const router = useRouter();
  const [isDeleted, setIsDeleted] = useState(false);
  const t = useTranslations();

  // registering dropdown elements manually
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationModalContent, setConfirmationModalContent] = useState<{
    title: string;
    description: string | JSX.Element;
    confirmButtonText: string;
    confirmButtonClass: string;
    onConfirm: () => void;
  } | null>(null);

  const handleShareModal = () => {
    setShowShareModal(true);
  };

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  // getting hashed project id and decoding it for api call
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const projectId = decode(hashedIdString);

  const { user } = useAuth();

  const queryClient = useQueryClient();

  // Cancel queries on unmount
  useEffect(() => {
    return () => {
      if (projectId && user?.id) {
        queryClient.cancelQueries({
          queryKey: ["projects", user.id, projectId],
        });
      }
    };
  }, [projectId, user?.id, queryClient]);

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id && !isDeleted,
  });

  // populating form fields with project data
  useEffect(() => {
    if (projectData) {
      reset({
        projectName: projectData.name || "",
        description: projectData.description || "",
        country: projectData.country || "",
        sector: projectData.sector || "",
      });

      setSelectedCountry(projectData.country || null);
      setSelectedSector(projectData.sector || null);
    }
  }, [projectData, reset]);

  const dispatch = useDispatch();

  // mutation function for updating project data
  const projectMutation = useMutation({
    mutationFn: updateProject,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["projects", user?.id],
        exact: false,
      });
      dispatch(
        showNotification({
          message: t('projectUpdated'),
          type: "success",
        })
      );
    },
    onError: (error) => {
      dispatch(
        showNotification({
          message:
            t('projectUpdateFailed') +
            error.message,
          type: "error",
        })
      );
    },
  });

  //deploy mutation
  const deployProjectMutation = useMutation<unknown, Error, { isUnarchive?: boolean }>({
    mutationFn: (variables) => deployProject(projectId!, variables?.isUnarchive || false),
    onSuccess: (_, variables) => {
      const isUnarchive = variables?.isUnarchive || false;
      queryClient.invalidateQueries({ queryKey: ["projects", user?.id] });
      dispatch(
        showNotification({
          message: isUnarchive
            ? t('projectUnarchived')
            : t('projectDeployed'),
          type: "success",
        })
      );
      setShowConfirmationModal(false);
    },
    onError: (error: any) => {
      dispatch(
        showNotification({
          message: t('projectDeployFailed'),
          type: "error",
        })
      );
      setShowConfirmationModal(false);
    },
  });

  //mutation function for archiving project
  const archiveProjectMutation = useMutation({
    mutationFn: () => archiveProject(projectId!),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["projects", user?.id, projectId],
      });
      dispatch(
        showNotification({
          message: t('projectArchived'),
          type: "success",
        })
      );
      setShowConfirmationModal(false);
      router.push("/dashboard"); // optional: redirect

      // Also invalidate the projects list
      queryClient.invalidateQueries({ queryKey: ["projects", user?.id] });
    },
    onError: (error: any) => {
      console.error("Project archive error:", error);
      dispatch(
        showNotification({
          message: t('projectArchiveFailed'),
          type: "error",
        })
      );
      setShowConfirmationModal(false);
    },
  });

  // mutation function for deleting project
  const deleteProjectMutation = useMutation({
    mutationFn: () => deleteProject(projectId!),
    onSuccess: () => {
      setIsDeleted(true);
      setShowConfirmationModal(false);

      // Cancel all queries for this project
      queryClient.cancelQueries({
        queryKey: ["projects", user?.id, projectId],
      });

      // Completely remove the query from the cache
      queryClient.removeQueries({
        queryKey: ["projects", user?.id, projectId],
      });

      // Also invalidate the projects list
      queryClient.invalidateQueries({ queryKey: ["projects", user?.id] });

      // Show success notification
      dispatch(
        showNotification({
          message: t('projectDeleted'),
          type: "success",
        })
      );

      // Redirect after short delay for notification to be seen
      setTimeout(() => {
        router.push("/dashboard");
      }, 1000);
    },
    onError: (error: any) => {
      setShowConfirmationModal(false);
      console.error("Project deletion error:", error);

      dispatch(
        showNotification({
          message: t('projectDeleteFailed'),
          type: "error",
        })
      );
    },
  });

  const handleArchiveClick = () => {
    setConfirmationModalContent({
      title: t('confirmArchive'),
      description: (
        <>
          {t('confirmArchiveMessage')}
        </>
      ),
      confirmButtonText: t('archive'),
      confirmButtonClass: "btn-primary",
      onConfirm: () => {
        archiveProjectMutation.mutate();
      },
    });
    setShowConfirmationModal(true);
  };

  const handleDeployClick = () => {
    const isUnarchiving = projectData?.status === "archived";

    let description = t('confirmDeployMessage');

    if (projectData?.status === "deployed") {
      description = t('confirmRedeployMessage');
    } else if (projectData?.status === "archived") {
      description = t('confirmDeployMessage');
    }

    setConfirmationModalContent({
      title: isUnarchiving ? t('confirmUnarchive') : t('confirmDeploy'),
      description,
      confirmButtonText: isUnarchiving ? t('unarchive') : t('deploy'),
      confirmButtonClass: "btn-primary",
      onConfirm: () => {
        deployProjectMutation.mutate({ isUnarchive: isUnarchiving });
      },
    });

    setShowConfirmationModal(true);
  };

  const onSubmit = async (data: FieldValues) => {
    projectMutation.mutate({
      projectId: projectId!,
      dataToSend: {
        name: data.projectName,
        description: data.description,
        country: data.country,
        sector: data.sector,
      },
    });
  };

  const handleDeleteClick = () => {
    setConfirmationModalContent({
      title: t('confirmDelete'),
      description: (
        <>
          <p>
            {t('confirmDeleteMessage')}
          </p>
          <ul className="list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700">
            <li>{t('deleteProjectWarning1')}</li>
            <li>{t('deleteProjectWarning2')}</li>
            <li>
              {t('deleteProjectWarning3')}
            </li>
          </ul>
        </>
      ),
      confirmButtonText: t('delete'),
      confirmButtonClass: "btn-danger",
      onConfirm: () => {
        deleteProjectMutation.mutate();
      },
    });
    setShowConfirmationModal(true);
  };

  // To prevent errors from showing when the component is not fully mounted.
  if (!hasMounted) return null;

  if (isDeleted) {
    return <Spinner />;
  }

  if (projectLoading) {
    return <Spinner />;
  }

  // If hashedId is missing, show an error
  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('invalidProjectIdError')}</h1>
        <p className="text-neutral-700">
          {t('invalidProjectIdMessage')}
        </p>
      </div>
    );
  }

  if (projectError && !isDeleted) {
    return (
      <p className="text-red-500">{t('fetchProjectFailed')}</p>
    );
  }

  return (
    <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-4">
        {/* Project Name */}
        <div className="label-input-group group">
          <label htmlFor="project-name" className="label-text">
            <FileText size={16} /> {t('projectName')}
          </label>
          <input
            {...register("projectName", {
              required: t('projectNameRequired'),
            })}
            id="project-name"
            type="text"
            className="input-field"
            placeholder={t('enterProjectName')}
          />
          {errors.projectName && (
            <p className="text-red-500 text-sm">{`${errors.projectName.message}`}</p>
          )}
        </div>
        {/* Project Description */}
        <div className="label-input-group group">
          <label htmlFor="description" className="label-text">
            {t('description')}
          </label>
          <textarea
            id="description"
            {...register("description")}
            className="input-field resize-none"
            cols={4}
            placeholder={t('enterProjectDescription')}
          />
        </div>
        {/* Country and Sector */}
        <div className="grid grid-cols-2 gap-4">
          <div className="label-input-group group">
            <label htmlFor="country" className="label-text">
              <Globe size={16} />
              {t('country')}
            </label>
            <Select
              id="country"
              options={countries}
              value={selectedCountry}
              onChange={setSelectedCountry}
            />
            {errors.country && (
              <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="sector" className="label-text">
              <Briefcase size={16} /> {t('sector')}
            </label>
            <Select
              id={`sector`}
              options={Object.values(SectorLabelMap)} // Display labels
              value={
                selectedSector && SectorLabelMap[selectedSector]
                  ? SectorLabelMap[selectedSector]
                  : t('selectOption')
              }
              onChange={(label) => {
                const selectedKey = labelToKey(label, SectorLabelMap);
                setSelectedSector(selectedKey); // Set the enum key for storage
              }}
            />
            {errors.sector && (
              <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
            )}
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {projectData?.status === "deployed" && (
              <button
                onClick={handleArchiveClick}
                type="button"
                className="btn-outline"
              >
                {t('archive')}
              </button>
            )}

            {projectData?.status === "deployed" && (
              <button
                onClick={handleDeployClick}
                type="button"
                className="btn-outline"
              >
                {t('redeploy')}
              </button>
            )}

            {projectData?.status === "archived" && (
              <button
                onClick={handleDeployClick}
                type="button"
                className="btn-outline" //Unarchive button
              >
                {t('deploy')}
              </button>
            )}

            {projectData?.status === "draft" && (
              <button
                onClick={handleDeployClick}
                type="button"
                className="btn-outline"
              >
                {t('deploy')}
              </button>
            )}

            <button
              type="button"
              className="btn-outline"
              onClick={handleShareModal}
            >
              {t('share')}
            </button>

            <button
              onClick={handleDeleteClick}
              type="button"
              className="btn-danger"
            >
              {t('delete')}
            </button>
          </div>
          <button type="submit" className="btn-primary self-end">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('saving')}
                <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
              </span>
            ) : (
              t('saveChanges')
            )}
          </button>
        </div>
      </div>
      <ShareProjectModal
        showModal={showShareModal}
        onClose={() => setShowShareModal(false)}
        onShare={() => {
          setShowShareModal(false);
        }}
      />

      {confirmationModalContent && (
        <ConfirmationModal
          showModal={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          title={confirmationModalContent.title}
          description={confirmationModalContent.description}
          confirmButtonText={confirmationModalContent.confirmButtonText}
          confirmButtonClass={confirmationModalContent.confirmButtonClass}
          onConfirm={confirmationModalContent.onConfirm}
        />
      )}
    </form>
  );
};

export default ProjectSettingsPage;
