import axios from "@/lib/axios";

/**
 * Fetch all question groups for a project
 */
export const fetchQuestionGroups = async ({
  projectId,
}: {
  projectId: number;
}) => {
  try {
    // Use the project endpoint to fetch the project with its question groups
    const { data } = await axios.get(`/projects/form/${projectId}`);

    // Extract question groups from the project data
    const questionGroups = data.data?.project?.questionGroup || [];
    return questionGroups;
  } catch (error) {
    console.error(
      "Error fetching question groups from project endpoint:",
      error
    );

    // Fallback to direct question groups endpoint
    try {
      const { data } = await axios.post(`/question-groups`, { projectId });
      return data.data?.projectGroup || [];
    } catch (fallbackError) {
      console.error("Error in fallback fetch:", fallbackError);

      // Last resort: create a dummy group for debugging
      if (process.env.NODE_ENV === "development") {
        return [];
      }

      return [];
    }
  }
};

/**
 * Create a new question group
 */
export const createQuestionGroup = async ({
  title,
  order,
  projectId,
  selectedQuestionIds,
  parentGroupId,
}: {
  title: string;
  order: number;
  projectId: number;
  selectedQuestionIds?: number[];
  parentGroupId?: number;
}) => {
  try {
    const { data } = await axios.post(`/question-groups`, {
      title,
      order,
      projectId,
      selectedQuestionIds: selectedQuestionIds || [],
      parentGroupId,
    });

    return data;
  } catch (error) {
    console.error("Error creating question group:", error);
    throw error;
  }
};

/**
 * Update an existing question group
 */
export const updateQuestionGroup = async ({
  id,
  title,
  order,
  selectedQuestionIds,
}: {
  id: number;
  title: string;
  order: number;
  selectedQuestionIds?: number[];
}) => {
  try {
    const { data } = await axios.patch(`/question-groups`, {
      id,
      title,
      order,
      selectedQuestionIds,
    });

    return data;
  } catch (error) {
    console.error("Error updating question group:", error);
    throw error;
  }
};

/**
 * Delete a question group
 */
export const deleteQuestionGroup = async ({ id }: { id: number }) => {
  try {
    const { data } = await axios.delete(`/question-groups/${id}`);
    return data;
  } catch (error) {
    console.error("Error deleting question group:", error);
    throw error;
  }
};

/**
 * Delete a question group and all its questions
 */
export const deleteQuestionAndGroup = async ({ id }: { id: number }) => {
  try {
    const { data } = await axios.delete(
      `/question-groups/group/question/${id}`
    );
    return data;
  } catch (error) {
    console.error("Error deleting question group and questions:", error);
    throw error;
  }
};

/**
 * Remove a question from a group
 */
export const removeQuestionFromGroup = async ({
  groupId,
  questionId,
}: {
  groupId: number;
  questionId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/question/remove`, {
    groupId,
    questionId,
  });
  return data;
};

/**
 * Move a question from one group to another
 */
export const moveQuestionBetweenGroups = async ({
  groupId,
  newGroupId,
  questionId,
}: {
  groupId: number;
  newGroupId: number;
  questionId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/question/move`, {
    groupId,
    newGroupId,
    questionId,
  });
  return data;
};

/**
 * Move a group inside another group (create nested group)
 */
export const moveGroupInsideGroup = async ({
  childGroupId,
  parentGroupId,
}: {
  childGroupId: number;
  parentGroupId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/group/add`, {
    childGroupId,
    ParentGroupId: parentGroupId,
  });
  return data;
};

/**
 * Remove a group from its parent group (make it top-level)
 */
export const removeGroupFromParent = async ({
  groupId,
}: {
  groupId: number;
}) => {
  const { data } = await axios.patch(`/question-groups/group/remove`, {
    groupId,
  });
  return data;
};

/**
 * Update group order/positions
 */
export const updateGroupPositions = async ({
  projectId,
  groupPositions,
}: {
  projectId: number;
  groupPositions: { id: number; order: number; parentGroupId?: number }[];
}) => {
  const { data } = await axios.patch(`/question-groups/positions`, {
    projectId,
    groupPositions,
  });
  return data;
};
