'use client';

import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';

export function useLocaleRouter() {
  const locale = useLocale();
  const router = useRouter();

  const push = (path: string) => {
    // If path already starts with locale, don't add it again
    if (path.startsWith(`/${locale}`)) {
      router.push(path);
    } else {
      // Add locale prefix to path
      const localePath = path.startsWith('/') 
        ? `/${locale}${path}` 
        : `/${locale}/${path}`;
      router.push(localePath);
    }
  };

  return {
    push,
    locale
  };
}