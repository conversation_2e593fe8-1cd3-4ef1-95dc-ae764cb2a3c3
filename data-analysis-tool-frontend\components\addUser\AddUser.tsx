import React, { useState, useRef } from "react";
import { X } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { addProjectUser, checkUserExists } from "@/lib/api/projects";
import { useTranslations } from "next-intl";

interface AddUserProps {
  onClose: () => void; // Callback when the modal/dialog closes
  projectId?: number; // Project ID to which the user will be added
  onUserAdded?: () => void; // Callback after successful addition
}

const AddUser = ({ onClose, projectId, onUserAdded }: AddUserProps) => {
  const t = useTranslations();
  
  // Move permissions array inside component where we can use t()
  const permissions = [
    { label: t('viewForm'), value: "viewForm" },
    { label: t('editForm'), value: "editForm" },
    { label: t('viewSubmissions'), value: "viewSubmissions" },
    { label: t('editSubmissions'), value: "editSubmissions" },
    { label: t('addSubmissions'), value: "addSubmissions" },
    { label: t('deleteSubmissions'), value: "deleteSubmissions" },
    { label: t('validateSubmissions'), value: "validateSubmissions" },
    { label: t('manageProject'), value: "manageProject" },
  ];

  const [email, setEmail] = useState("");
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [error, setError] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [userExists, setUserExists] = useState<boolean | null>(null);

  const queryClient = useQueryClient(); // Used to invalidate cached project user list
  const dispatch = useDispatch(); // Redux dispatch for showing notifications
  const emailCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Debounce timer ref

  // Email validation using basic but reliable regex
  const isValidEmail = (email: string) => {
    return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
  };

  // Toggle permissions when checkboxes are clicked
  const handlePermissionChange = (value: string) => {
    setSelectedPermissions(
      (prev) =>
        prev.includes(value)
          ? prev.filter((v) => v !== value) // remove if already selected
          : [...prev, value] // add if not selected
    );
  };

  // Mutation to verify if user with provided email exists
  const checkUserExistsMutation = useMutation({
    mutationFn: checkUserExists,
    onSuccess: () => {
      setUserExists(true);
      setError("");
    },
    onError: (error: any) => {
      setUserExists(false);
      const errorMessage =
        error.response?.data?.message || t('userNotFound');
      setError(errorMessage);
    },
    onSettled: () => {
      setIsVerifying(false);
    },
  });

  // Called on each email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    setUserExists(null);
    setError("");

    if (!newEmail) return;

    if (!isValidEmail(newEmail)) {
      setError(t('invalidEmail'));
      return;
    }

    // Cancel any previously scheduled user check
    if (emailCheckTimeoutRef.current) {
      clearTimeout(emailCheckTimeoutRef.current);
    }

    // Debounced call to verify email after 800ms
    emailCheckTimeoutRef.current = setTimeout(() => {
      setIsVerifying(true);
      checkUserExistsMutation.mutate(newEmail);
    }, 800);
  };

  // Form validation logic
  const isFormValid = () => {
    if (!email) {
      setError(t('emailRequired'));
      return false;
    }
    if (!isValidEmail(email)) {
      setError(t('invalidEmail'));
      return false;
    }
    if (!userExists) {
      setError(t('userNotFound'));
      return false;
    }
    if (selectedPermissions.length === 0) {
      setError(t('selectPermission'));
      return false;
    }
    setError("");
    return true;
  };

  // Mutation to add user to project with selected permissions
  const addUserMutation = useMutation({
    mutationFn: () => {
      // Convert permission array to object format required by API
      const permissionsObject = selectedPermissions.reduce(
        (accumulator, permission) => {
          accumulator[permission] = true;
          return accumulator;
        },
        {} as Record<string, boolean>
      );

      return addProjectUser({
        projectId: projectId!,
        email,
        permissions: permissionsObject,
      });
    },
    onSuccess: () => {
      // Invalidate cached project users to refetch fresh list
      queryClient.invalidateQueries({ queryKey: ["projectUsers", projectId] });
      dispatch(
        showNotification({
          message: t('userAdded'),
          type: "success",
        })
      );
      if (onUserAdded) onUserAdded();
      onClose();
    },
    onError: (error: any) => {
      // Handle and display error message from API or fallback
      let errorMessage: string;
      if (typeof error === "string") {
        errorMessage = error;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        errorMessage =
          typeof error.response.data.message === "object"
            ? JSON.stringify(error.response.data.message)
            : error.response.data.message;
      } else {
        errorMessage = t('failedToAddUser');
      }

      dispatch(
        showNotification({
          message: errorMessage,
          type: "error",
        })
      );
      setError(errorMessage);
    },
  });

  // Final submit handler for adding the user
  const handleSubmit = () => {
    if (!projectId) {
      setError(t('projectIdRequired'));
      return;
    }

    if (isFormValid()) {
      addUserMutation.mutate();
    }
  };

  return (
    <div className="bg-neutral-100 p-6 rounded-md">
      {/* Email input with validation */}
      <div className="relative">
        <input
          className={`w-full border ${
            error ? "border-red-500" : "border-neutral-300"
          } rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary-200 placeholder:text-neutral-500`}
          placeholder={t('email')}
          value={email}
          onChange={handleEmailChange}
        />
        {/* Close button for modal */}
        <button
          className="absolute right-2 top-2 text-neutral-700 hover:text-neutral-900"
          onClick={onClose}
          type="button"
        >
          <X size={22} />
        </button>
        {/* Status messages */}
        {isVerifying && (
          <p className="text-neutral-500 text-sm mb-2">{t('verifyingEmail')}</p>
        )}
        {userExists === true && (
          <p className="text-green-500 text-sm mb-2">{t('userFound')}</p>
        )}
        {error && <p className="text-red-500 text-sm mb-2">{error}</p>}
      </div>

      {/* Permissions checkboxes */}
      <div className="flex flex-col gap-2">
        {permissions.map((permission) => (
          <div key={permission.value} className="flex flex-col">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={selectedPermissions.includes(permission.value)}
                onChange={() => handlePermissionChange(permission.value)}
              />
              {permission.label}
            </label>
          </div>
        ))}
      </div>

      {/* Submit button */}
      <button
        className={`mt-6 ${
          addUserMutation.isPending || isVerifying
            ? "bg-neutral-400"
            : "bg-blue-400 hover:bg-blue-500"
        } text-white px-6 py-2 rounded disabled:opacity-50`}
        disabled={
          addUserMutation.isPending ||
          isVerifying ||
          !email ||
          selectedPermissions.length === 0 ||
          !userExists
        }
        onClick={handleSubmit}
      >
        {addUserMutation.isPending ? t('adding') : t('grantPermissions')}
      </button>
    </div>
  );
};

export { AddUser };
