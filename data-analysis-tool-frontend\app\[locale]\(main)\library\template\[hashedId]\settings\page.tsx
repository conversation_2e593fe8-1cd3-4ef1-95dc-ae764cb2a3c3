"use client";

import { JS<PERSON>, useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import axios from "@/lib/axios";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Template } from "@/types";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { Select } from "@/components/general/Select";
import { Globe, Briefcase, FileText } from "lucide-react";
import Spinner from "@/components/general/Spinner";
import { SectorLabelMap } from "@/constants/sectors";
import countries from "@/constants/countryNames.json";
import { labelToKey } from "@/lib/labelToKey";
import { useAuth } from "@/hooks/useAuth";
import { ShareProjectModal } from "@/components/modals/ShareProjectModal";
import { ConfirmationModal } from "@/components/modals/ConfirmationModal";
import { deleteTemplate, fetchTemplateById } from "@/lib/api/templates";
import { useTranslations } from "next-intl";

const updateTemplate = async ({
  templateId,
  dataToSend,
}: {
  templateId: number;
  dataToSend: {
    name: string;
    description: string;
    sector: string;
    country: string;
  };
}) => {
  const { data } = await axios.patch(`/libraries/${templateId}`, dataToSend);
  return data;
};

const TemplateSettingsPage = () => {
  const [hasMounted, setHasMounted] = useState<boolean>(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
    reset,
  } = useForm();

  const router = useRouter();
  const [isDeleted, setIsDeleted] = useState(false);
  const t = useTranslations();

  // registering dropdown elements manually
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [confirmationModalContent, setConfirmationModalContent] = useState<{
    title: string;
    description: string | JSX.Element;
    confirmButtonText: string;
    confirmButtonClass: string;
    onConfirm: () => void;
  } | null>(null);

  const handleShareModalClose = () => {
    setShowShareModal(true);
  };

  useEffect(() => {
    register("country", { required: t('pleaseSelectCountry') });
    register("sector", { required: t('pleaseSelectSector') });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  // getting hashed template id and decoding it for api call
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const templateId = decode(hashedIdString);

  const { user } = useAuth();

  const queryClient = useQueryClient();

  // Cancel queries on unmount
  useEffect(() => {
    return () => {
      if (templateId && user?.id) {
        queryClient.cancelQueries({
          queryKey: ["templates", user.id, templateId],
        });
      }
    };
  }, [templateId, user?.id, queryClient]);

  const {
    data: templateData,
    isLoading: templateLoading,
    isError: templateError,
  } = useQuery<Template>({
    queryKey: ["templates", user?.id, templateId],
    queryFn: () => fetchTemplateById({ templateId: templateId! }),
    enabled: !!templateId && !!user?.id,
  });

  // populating form fields with template data
  useEffect(() => {
    if (templateData) {
      reset({
        templateName: templateData.name || "",
        description: templateData.description || "",
        country: templateData.country || "",
        sector: templateData.sector || "",
      });

      setSelectedCountry(templateData.country || null);
      setSelectedSector(templateData.sector || null);
    }
  }, [templateData, reset]);

  const dispatch = useDispatch();

  // mutation function for updating template data
  const templateMutation = useMutation({
    mutationFn: updateTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["templates"],
        exact: false,
      });
      dispatch(
        showNotification({
          message: t('templateUpdated'),
          type: "success",
        })
      );
    },
    onError: (error) => {
      dispatch(
        showNotification({
          message:
            t('templateUpdateFailed') +
            error.message,
          type: "error",
        })
      );
    },
  });

  // mutation function for deleting template
  const deleteTemplateMutation = useMutation({
    mutationFn: () => deleteTemplate(templateId!),
    onSuccess: () => {
      setIsDeleted(true);
      setShowConfirmationModal(false);

      // Cancel all queries for this template
      queryClient.cancelQueries({
        queryKey: ["templates", user?.id, templateId],
      });

      // Completely remove the query from the cache
      queryClient.removeQueries({
        queryKey: ["template", user?.id, templateId],
      });

      // Also invalidate the templates list
      queryClient.invalidateQueries({ queryKey: ["templates"], exact: false });

      // Show success notification
      dispatch(
        showNotification({
          message: t('templateDeleted'),
          type: "success",
        })
      );

      // Redirect after short delay for notification to be seen
      setTimeout(() => {
        router.push("/library");
      }, 1000);
    },
    onError: (error) => {
      setShowConfirmationModal(false);
      console.error("Template deletion error:", error);

      dispatch(
        showNotification({
          message: t('templateDeleteFailed'),
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    templateMutation.mutate({
      templateId: templateId!,
      dataToSend: {
        name: data.templateName,
        description: data.description,
        country: data.country,
        sector: data.sector,
      },
    });
  };

  const handleDeleteClick = () => {
    setConfirmationModalContent({
      title: t('confirmDelete'),
      description: (
        <>
          <p>
            {t('templateDeleteConfirm')}
          </p>
          <ul className="list-disc list-inside flex flex-col gap-2 mt-2 text-neutral-700">
            <li>{t('templateDeleteWarningData')}</li>
            <li>{t('templateDeleteWarningForms')}</li>
            <li>
             {t('templateDeleteWarningRecover')}
            </li>
          </ul>
        </>
      ),
      confirmButtonText: "Delete",
      confirmButtonClass: "btn-danger",
      onConfirm: () => {
        deleteTemplateMutation.mutate();
      },
    });
    setShowConfirmationModal(true);
  };

  // To prevent errors from showing when the component is not fully mounted.
  if (!hasMounted) return null;

  if (isDeleted) {
    return <Spinner />;
  }

  if (templateLoading) {
    return <Spinner />;
  }

  // If hashedId is missing, show an error
  if (!hashedId || templateId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500">{t('invalidTemplateIdError')}</h1>
        <p className="text-neutral-700">
          {t('invalidTemplateUrl')}
        </p>
      </div>
    );
  }

  if (templateError && !isDeleted) {
    return (
      <p className="text-red-500">
        {t('failedFetchTemplate')}
      </p>
    );
  }

  return (
    <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
      <div className="flex flex-col gap-4">
        {/* Template Name */}
        <div className="label-input-group group">
          <label htmlFor="template-name" className="label-text">
            <FileText size={16} /> {t('templateName')}
          </label>
          <input
            {...register("templateName", {
              required: t('templateNameRequired'),
            })}
            id="template-name"
            type="text"
            className="input-field"
            placeholder={t('templateNamePlaceholder')}
          />
          {errors.templateName && (
            <p className="text-red-500 text-sm">{`${errors.templateName.message}`}</p>
          )}
        </div>
        {/* Template Description */}
        <div className="label-input-group group">
          <label htmlFor="description" className="label-text">
            {t('description')}
          </label>
          <textarea
            id="description"
            {...register("description")}
            className="input-field resize-none"
            cols={4}
            placeholder={t('templateDescriptionPlaceholder')}
          />
        </div>
        {/* Country and Sector */}
        <div className="grid grid-cols-2 gap-4">
          <div className="label-input-group group">
            <label htmlFor="country" className="label-text">
              <Globe size={16} />
              {t('country')}
            </label>
            <Select
              id="country"
              options={countries}
              value={selectedCountry}
              onChange={setSelectedCountry}
            />
            {errors.country && (
              <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="sector" className="label-text">
              <Briefcase size={16} /> {t('sector')}
            </label>
            <Select
              id={`sector`}
              options={Object.values(SectorLabelMap)} // Display labels
              value={
                selectedSector && SectorLabelMap[selectedSector]
                  ? SectorLabelMap[selectedSector]
                  : t('selectOption')
              }
              onChange={(label) => {
                const selectedKey = labelToKey(label, SectorLabelMap);
                setSelectedSector(selectedKey); // Set the enum key for storage
              }}
            />
            {errors.sector && (
              <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
            )}
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* uncomment if required */}
            {/* <button
              type="button"
              className="btn-outline"
              onClick={handleShareModalClose}
            >
              share
            </button> */}
            <button
              onClick={handleDeleteClick}
              type="button"
              className="btn-danger"
            >
              {t('delete')}
            </button>
          </div>
          <button type="submit" className="btn-primary self-end">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('saving')}
                <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
              </span>
            ) : (
              t('saveChanges')
            )}
          </button>
        </div>
      </div>

      {/* change to share template modal if required otherwise remove it */}

      {/* <ShareProjectModal
        showModal={showShareModal}
        onClose={() => setShowShareModal(false)}
        onShare={() => {
          setShowShareModal(false);
        }}
      /> */}

      {confirmationModalContent && (
        <ConfirmationModal
          showModal={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          title={confirmationModalContent.title}
          description={confirmationModalContent.description}
          confirmButtonText={confirmationModalContent.confirmButtonText}
          confirmButtonClass={confirmationModalContent.confirmButtonClass}
          onConfirm={confirmationModalContent.onConfirm}
        />
      )}
    </form>
  );
};

export default TemplateSettingsPage;
