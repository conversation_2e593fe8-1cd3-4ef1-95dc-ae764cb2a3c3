"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Briefcase, Globe, ShieldCheck, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { z } from "zod";
import { AxiosError } from "axios";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { Select } from "@/components/general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { OrganizationTypeLabelMap } from "@/constants/organizationType";
import { labelToKey } from "@/lib/labelToKey";
import axios from "@/lib/axios";
import { useTranslations } from "next-intl";
import LanguageSwitcher from "@/components/LanguageSwitcher";

// Move schema into a function that takes t as an argument
const getSignUpSchema = (t: ReturnType<typeof useTranslations>) =>
  z
    .object({
      name: z.string().min(1, t('fullNameRequired')),
      email: z
        .string()
        .min(1, t('emailRequired'))
        .email(t('invalidEmail')),
      password: z
        .string()
        .min(1, t('passwordRequired'))
        .min(8, t('passwordMin'))
        .max(32, t('passwordMax'))
        .regex(/[A-Z]/, t('passwordUppercase'))
        .regex(/[a-z]/, t('passwordLowercase'))
        .regex(/[0-9]/, t('passwordNumber'))
        .regex(
          /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/,
          t('passwordSpecial')
        ),
      confirmPassword: z.string().min(1, t('confirmPasswordRequired')),
      country: z.string().min(1, t('selectCountry')),
      sector: z.string().min(1, t('selectSector')),
      organizationType: z.string().min(1, t('selectOrgType')),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t('passwordsDoNotMatch'),
      path: ["confirmPassword"],
    });

type SignUpFormValues = z.infer<ReturnType<typeof getSignUpSchema>>;

const page = () => {
  const t = useTranslations();
  const signUpSchema = getSignUpSchema(t);

  const {
    register,
    formState: { errors, isSubmitting, isSubmitted },
    setValue,
    handleSubmit,
    setError,
    watch,
  } = useForm<SignUpFormValues>({ resolver: zodResolver(signUpSchema) });

  // Watch password fields to determine when to show the eye button
  const passwordValue = watch("password");
  const confirmPasswordValue = watch("confirmPassword");

  useEffect(() => {
    register("country", { required: t('selectCountry') });
    register("sector", { required: t('selectSector') });
    register("organizationType", {
      required: t('selectOrgType'),
    });
  }, [register]);

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [selectedSector, setSelectedSector] = useState<string>("");
  const [selectedOrganizationType, setSelectedOrganizationType] =
    useState<string>("");

  // Password visibility states
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
    setValue("organizationType", selectedOrganizationType, {
      shouldValidate: isSubmitted,
    });
  }, [selectedCountry, selectedSector, selectedOrganizationType, setValue]);

  const router = useRouter();
  const dispatch = useDispatch();

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/signup`, data);
      router.push("/");
      dispatch(
        showNotification({
          message:
           t('signupSuccess'),
          type: "success",
        })
      );
    } catch (error) {
      if (error instanceof AxiosError) {
        setError(error.response?.data.errorField, {
          message: error.response?.data.message,
        });
      } else {
        dispatch(
          showNotification({
            message:
              t('signupError'),
            type: "error",
          })
        );
      }
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="flex flex-col gap-8 section w-11/12 mobile:w-4/5 tablet:w-2xl my-8 tablet:my-16">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            {t('createAccount')}
          </h1>
          <p className="text-neutral-700 text-center">
           {t('getStarted2')}
          </p>
        </div>
        <form className="flex flex-col gap-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="group label-input-group">
            <label htmlFor="name" className="label-text">
              {t('fullName')}
            </label>
            <input
              {...register("name")}
              id="name"
              type="text"
              placeholder={t('enterFullName')}
              className="input-field"
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{`${errors.name.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="email" className="label-text">
              {t('email')}
            </label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder={t('enterEmail')}
              className="input-field"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{`${errors.email.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="password" className="label-text">
              {t('password')}
            </label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder={t('enterPassword')}
                className="input-field w-full pr-10"
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{`${errors.password.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="confirm-password" className="label-text">
              {t('confirmPassword')}
            </label>
            <div className="relative">
              <input
                {...register("confirmPassword")}
                id="confirm-password"
                type={showConfirmPassword ? "text" : "password"}
                placeholder={t('confirm_password_required')}
                className="input-field w-full pr-10"
              />
              {confirmPasswordValue && confirmPasswordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showConfirmPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-sm">{`${errors.confirmPassword.message}`}</p>
            )}
          </div>

          <div className="grid grid-cols-1 tablet:grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="country" className="label-text">
                <Globe size={16} /> {t('country')}
              </label>
              <Select
                id={`country`}
                options={countries}
                value={selectedCountry || t('selectOption')}
                onChange={setSelectedCountry}
              />
              {errors.country && (
                <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
              )}
            </div>
            {/* Sector */}
            <div className="label-input-group group">
              <label htmlFor="sector" className="label-text">
                <Briefcase size={16} /> {t('sector')}
              </label>
              <Select
                id={`sector`}
                options={Object.values(SectorLabelMap)} // Display labels
                value={
                  selectedSector && SectorLabelMap[selectedSector]
                    ? SectorLabelMap[selectedSector]
                    : t('selectOption')
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(label, SectorLabelMap);
                  setSelectedSector(selectedKey ?? ""); // Set the enum key for storage
                }}
              />
              {errors.sector && (
                <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
              )}
            </div>
            {/* organization type */}
            <div className="label-input-group group">
              <label htmlFor="organizationType" className="label-text">
                <Briefcase size={16} /> {t('organizationType')}
              </label>
              <Select
                id={`organizationType`}
                options={Object.values(OrganizationTypeLabelMap)} // Display organization type labels
                value={
                  selectedOrganizationType &&
                  OrganizationTypeLabelMap[selectedOrganizationType]
                    ? OrganizationTypeLabelMap[selectedOrganizationType]
                    : t('selectOption')
                }
                onChange={(label) => {
                  const selectedKey = labelToKey(
                    label,
                    OrganizationTypeLabelMap
                  );
                  setSelectedOrganizationType(selectedKey ?? ""); // Set the enum key for organization type
                }}
              />
              {errors.organizationType && (
                <p className="text-red-500 text-sm">{`${errors.organizationType.message}`}</p>
              )}
            </div>
          </div>
          {/* country */}

          <button type="submit" className="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('signingUp')}
                <div className="size-4 rounded-full border-x-2 animate-spin"></div>
              </span>
            ) : (
              t('signUp')
            )}
          </button>
        </form>
        <div className="text-neutral-700 flex items-center gap-2">
          <span>{t('alreadyHaveAccount')}</span>
          <Link
            href="/"
            className="font-medium hover:text-neutral-900 duration-300"
          >
            {t('signIn')}
          </Link>
        </div>
        <div>
          <LanguageSwitcher />
        </div>
      </div>
    </div>
  );
};

export default page;
