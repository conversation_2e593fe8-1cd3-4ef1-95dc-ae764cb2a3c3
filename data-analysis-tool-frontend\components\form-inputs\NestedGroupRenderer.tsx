"use client";

import React, { useState } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { ChevronDown, ChevronRight } from "lucide-react";
import NestedQuestionRenderer from "./NestedQuestionRenderer";

interface NestedGroupRendererProps {
  group: QuestionGroup;
  nestingLevel?: number;
  visibleQuestions: Question[];
  nestedQuestions: Array<{
    question: Question;
    isVisible: boolean;
    isFollowUp: boolean;
    followUps: Array<{
      question: Question;
      isVisible: boolean;
    }>;
  }>;
  renderQuestionInput: (question: Question) => React.ReactNode;
  errors: Record<string, string>;
  onToggleExpansion?: (groupId: number) => void;
  isExpanded?: boolean;
  expandedGroups?: Record<number, boolean>; // Add this to pass expansion state for subgroups
  className?: string;
}

const NestedGroupRenderer: React.FC<NestedGroupRendererProps> = ({
  group,
  nestingLevel = 0,
  visibleQuestions,
  nestedQuestions,
  renderQuestionInput,
  errors,
  onToggleExpansion,
  isExpanded: controlledExpanded,
  expandedGroups,
  className = "",
}) => {
  const [internalExpanded, setInternalExpanded] = useState(true);
  
  // Use controlled expansion if provided, otherwise use internal state
  const isExpanded = controlledExpanded !== undefined ? controlledExpanded : internalExpanded;
  
  const handleToggleExpansion = () => {
    if (onToggleExpansion) {
      onToggleExpansion(group.id);
    } else {
      setInternalExpanded(!internalExpanded);
    }
  };

  // Get questions for this group
  const groupQuestions = group.question || [];
  const visibleGroupQuestions = groupQuestions.filter((q) =>
    visibleQuestions.some((vq) => vq.id === q.id)
  );

  // Get subgroups for this group
  const subGroups = group.subGroups || [];
  const visibleSubGroups = subGroups.filter((subGroup) => {
    const subGroupQuestions = subGroup.question || [];
    return subGroupQuestions.some((q) =>
      visibleQuestions.some((vq) => vq.id === q.id)
    );
  });

  // Don't render if no visible questions in this group or its subgroups
  if (visibleGroupQuestions.length === 0 && visibleSubGroups.length === 0) {
    return null;
  }

  return (
    <div
      className={`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${
        nestingLevel > 0 ? `ml-8 border-l-4 border-l-primary-300` : ''
      } ${className}`}
    >
      {/* Group Header */}
      <div
        className="flex items-center justify-between p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md cursor-pointer hover:bg-neutral-200 dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600"
        onClick={handleToggleExpansion}
      >
        <div className="flex items-center space-x-2">
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-neutral-700 dark:text-neutral-300" />
          ) : (
            <ChevronRight className="h-5 w-5 text-neutral-700 dark:text-neutral-300" />
          )}
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
            {group.title}
          </h3>
          <span className="text-sm text-neutral-700 dark:text-neutral-400">
            ({visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg) => acc + (sg.question?.length || 0), 0)} visible question
            {(visibleGroupQuestions.length + visibleSubGroups.reduce((acc, sg) => acc + (sg.question?.length || 0), 0)) !== 1 ? "s" : ""})
          </span>
        </div>
      </div>

      {/* Group Content */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          {/* Render subgroups first */}
          {visibleSubGroups
            .sort((a, b) => a.order - b.order)
            .map((subGroup) => {
              // Get expansion state for this subgroup from parent's expandedGroups
              const subGroupExpanded = expandedGroups ? expandedGroups[subGroup.id] : undefined;

              return (
                <NestedGroupRenderer
                  key={subGroup.id}
                  group={subGroup}
                  nestingLevel={nestingLevel + 1}
                  visibleQuestions={visibleQuestions}
                  nestedQuestions={nestedQuestions}
                  renderQuestionInput={renderQuestionInput}
                  errors={errors}
                  onToggleExpansion={onToggleExpansion}
                  isExpanded={subGroupExpanded} // Use expansion state from parent
                  expandedGroups={expandedGroups} // Pass through for deeper nesting
                  className={className}
                />
              );
            })}

          {/* Render questions in this group */}
          {nestedQuestions
            .filter((nq) =>
              groupQuestions.some((gq) => gq.id === nq.question.id)
            )
            .map((questionGroup) => (
              <NestedQuestionRenderer
                key={questionGroup.question.id}
                questionGroup={questionGroup}
                renderQuestionInput={renderQuestionInput}
                errors={errors}
                className=""
              />
            ))}
        </div>
      )}
    </div>
  );
};

export default NestedGroupRenderer;
