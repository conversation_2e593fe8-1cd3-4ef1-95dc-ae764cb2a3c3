import { Checkbox } from "@/components/ui";
import { Question } from "@/types/formBuilder";
import { ColumnDef } from "@tanstack/react-table";
import { InputType } from "@/constants/inputType";
import { useTranslations } from "next-intl";

export const QuestionBlockListColumns= (): ColumnDef<Question>[] =>{

const t = useTranslations();
  return [
  {
    id: "sno",
    header: t('sn'),
    cell: ({ row }) => row.index + 1,
  },
  {
    accessorKey: "label",
    header: t('question'),
  },
  {
    accessorKey: "inputType",
    header: t('inputType'),
    cell: ({ row }) => {
      const inputType = row.getValue("inputType") as InputType;
      return inputType.charAt(0).toUpperCase() + inputType.slice(1);
    },
  },
  {
    accessorKey: "questionOptions",
    header: t('options'),
    cell: ({ row }) => {
      const inputType = row.original.inputType;
      if (inputType === "selectone" || inputType === "selectmany") {
        const options = row.original.questionOptions || [];
        return (
          <div className="space-y-1">
            {options.map((option, index) => (
              <div key={index} className="text-sm">
                <span className="font-medium">-{option.label}</span>
                {option.sublabel && (
                  <span className="text-neutral-500 ml-2">({option.sublabel})</span>
                )}
              </div>
            ))}
          </div>
        );
      }
      return null;
    },
  },
  {
    accessorKey: "hint",
    header: t('hint'),
  },
  {
    accessorKey: "isRequired",
    header: t('required'),
    cell: ({ row }) => {
      const isRequired = row.getValue("isRequired") as boolean;
      return isRequired ? "Yes" : "No";
    },
  },
  {
    accessorKey: "updatedAt",
    header: t('lastModified'),
    cell: ({ row }) => {
      const date = new Date(row.getValue("updatedAt"));
      return date.toLocaleDateString();
    },
  },
];
}

