"use client";

import React, { useEffect, useState } from "react";
import { ProjectListClient } from "@/components/ProjectListClient";
import axios from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";
import Spinner from "@/components/general/Spinner";
import { Project } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";

const fetchProjects = async () => {
  const { data } = await axios.get(`/projects`);
  return data.projects;
};

export default function ProjectListPage() {
  // replace with actual user id
  const { user } = useAuth();
  const t = useTranslations();

  const {
    data: projectsData,
    isLoading: projectsLoading,
    isError: projectsError,
  } = useQuery<Project[]>({
    queryKey: ["projects", user?.id],
    queryFn: fetchProjects,
    enabled: !!user?.id,
  });

  if (projectsLoading || !projectsData) {
    return <Spinner />;
  }

  if (projectsError) {
    return <p className="text-red-500">{t('error_loading_data')}</p>;
  }

  return <ProjectListClient data={projectsData} />;
}
