'use client';

import { useLocale } from 'next-intl';
import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';

export default function LanguageSwitcher() {
  const locale = useLocale();
  const pathname = usePathname();
  
  // Function to get the path for the alternate locale
  const getLocalizedPath = (newLocale: string) => {
    // Remove the current locale from the pathname if it exists
    const pathnameWithoutLocale = pathname.replace(/^\/(en|ne)/, '');
    return `/${newLocale}${pathnameWithoutLocale}`;
  };

  return (
    <div className="flex items-center gap-2">
      <Link 
        href={getLocalizedPath('en')}
        className={`px-3 py-1 rounded ${locale === 'en' ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}
      >
        English
      </Link>
      <Link 
        href={getLocalizedPath('ne')}
        className={`px-3 py-1 rounded ${locale === 'ne' ? 'bg-primary-600 text-white' : 'bg-gray-200'}`}
      >
        नेपाली
      </Link>
    </div>
  );
}