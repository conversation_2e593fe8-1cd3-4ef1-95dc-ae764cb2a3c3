"use client";

import React from "react";
import {
  ColumnDef,
  VisibilityState,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  OnChangeFn,
  SortingState,
  RowSelectionState,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { FaChevronDown } from "react-icons/fa";
import { useTranslations } from "next-intl";
interface GeneralTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  globalFilter?: string;
  setGlobalFilter?: (filterValue: string) => void;
  onTableInit?: (table: import("@tanstack/react-table").Table<TData>) => void;
  columnVisibility?: VisibilityState;
  setColumnVisibility?: (state: VisibilityState) => void;
  onRowSelectionChange?: (rowSelection: RowSelectionState) => void;
  rowSelection?: RowSelectionState;
  onRowClick?: (row: TData) => void;
}

const GeneralTable = <TData, TValue>({
  columns,
  data,
  globalFilter,
  setGlobalFilter,
  onTableInit,
  columnVisibility: externalColumnVisibility,
  setColumnVisibility: setExternalColumnVisibility,
  onRowSelectionChange,
  rowSelection: externalRowSelection,
  onRowClick,
}: GeneralTableProps<TData, TValue>) => {
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 8,
  });

  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);

  const t = useTranslations()

  const [internalColumnVisibility, setInternalColumnVisibility] =
    React.useState<VisibilityState>({});

  const effectiveColumnVisibility =
    externalColumnVisibility ?? internalColumnVisibility;
  const setEffectiveColumnVisibility =
    setExternalColumnVisibility ?? setInternalColumnVisibility;

  const [internalRowSelection, setInternalRowSelection] = React.useState<RowSelectionState>({});
  
  // Use external row selection if provided, otherwise use internal
  const effectiveRowSelection = externalRowSelection !== undefined 
    ? externalRowSelection 
    : internalRowSelection;

  const table = useReactTable({
    data,
    columns,
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange:
      setEffectiveColumnVisibility as OnChangeFn<VisibilityState>,
    onRowSelectionChange: (updater) => {
      const newRowSelection =
        typeof updater === "function" ? updater(effectiveRowSelection) : updater;
      
      // Only update internal state if we're not using external state
      if (externalRowSelection === undefined) {
        setInternalRowSelection(newRowSelection);
      }
      
      if (onRowSelectionChange) {
        onRowSelectionChange(newRowSelection);
      }
    },
    onSortingChange: setSorting,

    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(), // Ensure filtering is applied to the entire dataset
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableRowSelection: true,
    enableSorting: true,
    enableSortingRemoval: true,

    state: {
      pagination,
      columnFilters,
      globalFilter,
      columnVisibility: effectiveColumnVisibility,
      rowSelection: effectiveRowSelection,
      sorting,
    },
  });

  React.useEffect(() => {
    if (onTableInit) {
      onTableInit(table);
    }
  }, [onTableInit, table]);

  // Effect to handle external row selection changes
  React.useEffect(() => {
    if (externalRowSelection !== undefined) {
      // If we receive new external selection data, reset the table's row selection state
      table.setRowSelection(externalRowSelection);
    }
  }, [externalRowSelection, table]);

  return (
    <div>
      <div className="rounded-md border border-neutral-400 overflow-hidden">
        <Table className="min-w-full">
          <TableHeader className="h-20">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="text-sm border-neutral-400"
              >
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={`${headerGroup.id}_${header.id}_${header.index}`}
                    className={`py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold ${
                      header.index === 0 ? "w-12 py-3 px-6" : ""
                    }`}
                    style={{
                      cursor: header.column.getCanSort()
                        ? "pointer"
                        : "default",
                    }}
                  >
                    <div onClick={header.column.getToggleSortingHandler()}>
                      <div>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </div>
                    </div>
                    {header.column.id === "validation" ? (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            className="h-8 my-1 text-neutral-700 cursor-pointer"
                          >
                           {t('filter')}
                            <FaChevronDown />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-neutral-100 border border-neutral-200 shadow-md cursor-pointer">
                          <DropdownMenuItem
                            className="cursor-pointer hover:bg-neutral-300"
                            onClick={() =>
                              header.column.setFilterValue("Valid")
                            }
                          >
                            {t('valid')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="cursor-pointer hover:bg-neutral-300"
                            onClick={() =>
                              header.column.setFilterValue("Not Valid")
                            }
                          >
                            {t('notValid')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="cursor-pointer hover:bg-neutral-300"
                            onClick={() => header.column.setFilterValue("")}
                          >
                            {t('clearFilter')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    ) : (
                      header.column.getCanFilter() && (
                        <input
                          placeholder={t("search")}
                          value={
                            (header.column.getFilterValue() as string) || ""
                          }
                          onChange={(e) =>
                            header.column.setFilterValue(e.target.value)
                          }
                          className="input-field placeholder:font-semibold max-w-48 text-sm my-1 px-2 py-1 bg-neutral-100 text-neutral-700  border-none rounded-md"
                        />
                      )
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getPaginationRowModel().rows.length ? (
              table.getPaginationRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="hover:bg-neutral-50 text-sm border-neutral-400"
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell, index) => {
                    return (
                      <TableCell
                        key={`${row.id}_${cell.id}_${index}`}
                        className={`py-4 px-6 max-w-48  ${
                          index === 0 ? "py-3 px-6" : ""
                        } text-neutral-700 `}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {t('noResults')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} {t('rowSelected')}
        </div>

        {data.length > pagination.pageSize && (
          <div className="flex items-center justify-end space-x-2 py-4">
            <button
              className="btn-primary"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              {t('previous')}
            </button>
            <button
              className="btn-primary"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              {t('next')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export { GeneralTable };
