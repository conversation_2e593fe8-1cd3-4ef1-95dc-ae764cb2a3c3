"use client";

import React, { useState, useRef, useEffect } from "react";
import Modal from "./Modal";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table";

import { Input } from "@/components/ui/input";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { updateAnswer } from "@/lib/api/submission";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

import { Submission } from "@/app/[locale]/(main)/project/[hashedId]/data/table/columns";

// Transform submission data to a simpler format for the table
const transformSubmissionData = (submission: Submission) => {
  if (!submission || !submission.answers) return [];

  // Create a map to group answers by question
  const questionMap = new Map();

  submission.answers.forEach((answer) => {
    const questionId = answer.question?.id;
    const questionLabel = answer.question?.label || 'unknown';

    if (!questionId) return;

    if (!questionMap.has(questionId)) {
      questionMap.set(questionId, {
        type: answer.question?.inputType || "text",
        question: questionLabel,
        questionObject: answer.question,
        answers: [answer.value],
        originalData: [answer],
      });
    } else {
      const existingEntry = questionMap.get(questionId);
      existingEntry.answers.push(answer.value);
      existingEntry.originalData.push(answer);
    }
  });

  // Convert map to array
  return Array.from(questionMap.values());
};

const EditSubmissionModal = ({
  showModal,
  onClose,
  onConfirm,
  submission,
  isMultipleSelection = false,
  selectedSubmissions = [],
  projectId,
}: {
  showModal: boolean;
  onClose: () => void;
  onConfirm: () => void;
  submission: Submission;
  isMultipleSelection?: boolean;
  selectedSubmissions?: Submission[];
  projectId: number;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingData, setEditingData] = useState<any>(null);
  const [newResponse, setNewResponse] = useState("");
  const [selectedSubmissionId, setSelectedSubmissionId] = useState<
    number | null
  >(null);

  // Add dispatch for notifications
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const t = useTranslations();

  // Create refs for the input elements
  const questionInputRef = useRef<HTMLInputElement>(null);
  const answerInputRef = useRef<HTMLInputElement>(null);

  // Separate state for filtering
  const [questionFilter, setQuestionFilter] = useState("");
  const [answerFilter, setAnswerFilter] = useState("");

  // Transform the submission data for display
  const data = React.useMemo(() => {
    if (isMultipleSelection && selectedSubmissionId) {
      // Find the selected submission from the array of selected submissions
      const selectedSubmission = selectedSubmissions.find(
        (submission) => submission.id === selectedSubmissionId
      );
      return selectedSubmission
        ? transformSubmissionData(selectedSubmission)
        : [];
    }
    return transformSubmissionData(submission);
  }, [
    submission,
    isMultipleSelection,
    selectedSubmissionId,
    selectedSubmissions,
  ]);

  // Set default selected submission when modal opens
  useEffect(() => {
    if (
      isMultipleSelection &&
      selectedSubmissions.length > 0 &&
      !selectedSubmissionId
    ) {
      const firstId = selectedSubmissions[0]?.id;
      if (firstId !== undefined) {
        setSelectedSubmissionId(firstId);
      }
    }
  }, [isMultipleSelection, selectedSubmissions, selectedSubmissionId]);

  // Filter the data directly
  const filteredData = React.useMemo(() => {
    return data.filter((item) => {
      const questionMatches =
        item.question.toLowerCase().includes(questionFilter.toLowerCase()) ||
        !questionFilter;
      const answerMatches =
        String(item.answers)
          .toLowerCase()
          .includes(answerFilter.toLowerCase()) || !answerFilter;
      return questionMatches && answerMatches;
    });
  }, [data, questionFilter, answerFilter]);

  // Handle submission selection change
  const handleSubmissionChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedSubmissionId(Number(event.target.value));
  };

  const table = useReactTable({
    data: filteredData,
    columns: [
      {
        accessorKey: "type",
        header: t('type'),
      },
      {
        accessorKey: "question",
        header: () => (
          <div>
            <div>Question</div>
            <Input
              ref={questionInputRef}
              placeholder={t('searchQuestions')}
              value={questionFilter}
              onChange={(e) => setQuestionFilter(e.target.value)}
              className="bg-neutral-100 text-neutral-700 mt-2 h-8"
            />
          </div>
        ),
      },
      {
        accessorKey: "answers",
        header: () => (
          <div>
            <div>Answer</div>
            <Input
              ref={answerInputRef}
              placeholder={t('searchAnswers')}
              value={answerFilter}
              onChange={(e) => setAnswerFilter(e.target.value)}
              className="bg-neutral-100 text-neutral-700 mt-2 h-8"
            />
          </div>
        ),
        cell: ({ row }) => {
          const answers = row.original.answers;

          // If multiple submissions are selected, show special text
          if (isMultipleSelection) {
            return (
              <div className="flex flex-col">
                <div className="text-neutral-800 italic">
                 {t('multipleResponses')}
                </div>
              </div>
            );
          }

          return (
            <div className="flex flex-col">
              {answers.map((answer: any, index: number) => (
                <div key={index} className={index > 0 ? "" : ""}>
                  {String(answer)}
                </div>
              ))}
            </div>
          );
        },
      },
      {
        accessorKey: "action",
        header: t('action'),
        cell: ({ row }) => (
          <button
            className="btn-primary"
            onClick={() => handleEdit(row.original)}
          >
            {t('edit')}
          </button>
        ),
      },
    ],
    getCoreRowModel: getCoreRowModel(),
  });

  const handleEdit = (rowData: any) => {
    setIsEditing(true);
    setEditingData(rowData);
    // Join multiple answers with line breaks for editing
    setNewResponse(rowData.answers.join("\n"));
  };

  // Define mutation for updating answers
  const updateAnswerMutation = useMutation({
    mutationFn: (data: {
      submissionId: number;
      questionId: number | null | undefined;
      answerType: string;
      value: string | string[] | number;
      questionOptionId?: number | number[];
    }) => {
      // Make sure the questionId is valid
      if (!data.questionId) {
        throw new Error(t('questionIdRequired'));
      }

      // Make sure the submissionId is valid
      if (!data.submissionId) {
        throw new Error(t('submissionIdRequired'));
      }

      // Set up the payload according to backend expectations
      const payload: {
        submissionId: number;
        questionId: number;
        answerType: string;
        value: any;
        questionOptionId?: number | number[];
      } = {
        submissionId: data.submissionId,
        questionId: data.questionId as number, // Type assertion since we checked it's not null
        answerType: data.answerType,
        value: data.value,
      };

      // Handle questionOptionId correctly based on answerType
      if (data.answerType === "selectmany") {
        // For selectmany, questionOptionId must be an array
        payload.questionOptionId = Array.isArray(data.questionOptionId)
          ? data.questionOptionId
          : data.questionOptionId
          ? [data.questionOptionId]
          : []; // Convert to array if not already
      } else if (data.questionOptionId !== undefined) {
        // For other types, questionOptionId must NOT be an array
        payload.questionOptionId = Array.isArray(data.questionOptionId)
          ? data.questionOptionId[0] // Take first if it's incorrectly an array
          : data.questionOptionId;
      }

      // Ensure value type matches answerType requirements
      if (data.answerType === "number" || data.answerType === "decimal") {
        // Ensure it's a number
        payload.value =
          typeof data.value === "string"
            ? parseFloat(data.value)
            : typeof data.value === "number"
            ? data.value
            : 0;
      } else if (data.answerType === "selectmany") {
        // For selectmany, value must be an array of strings
        payload.value = Array.isArray(data.value)
          ? data.value.map((v) => String(v))
          : [String(data.value)];
      } else {
        // For all other types, ensure value is a string
        payload.value = String(data.value);
      }

      // Send the request
      return updateAnswer(payload, projectId);
    },
    onSuccess: (response) => {
      dispatch(
        showNotification({
          message: t('answerUpdated'),
          type: "success",
        })
      );

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["formSubmissions"] });

      // Reset edit state
      setIsEditing(false);
      setEditingData(null);
      setNewResponse("");

      // Call onConfirm to close modal and clear selections
      onConfirm();
    },
    onError: (error: any) => {
      console.error("Error updating answer:", error);

      // Log full error details
      console.error("Error details:", {
        response: error?.response?.data,
        status: error?.response?.status,
        headers: error?.response?.headers,
      });

      // Extract more specific error message if available
      const errorMessage =
        error?.response?.data?.message ||
        error?.response?.data?.errors ||
        "Failed to update answer";

      dispatch(
        showNotification({
          message:
            typeof errorMessage === "string"
              ? errorMessage
              : t('validationError'),
          type: "error",
        })
      );
    },
  });

  const handleSave = async () => {
    if (!editingData) return;

    // Get the current working submission
    const currentSubmission =
      isMultipleSelection && selectedSubmissionId
        ? selectedSubmissions.find(
            (submission) => submission.id === selectedSubmissionId
          ) || submission
        : submission;

    // Check if submission has ID
    if (!currentSubmission?.id) {
      dispatch(
        showNotification({
          message: t('submissionIdMissing'),
          type: "error",
        })
      );
      return;
    }

    // Split new response by line breaks to get individual answers
    const newAnswers = newResponse
      .split("\n")
      .map((answer) => answer.trim())
      .filter((answer) => answer);

    // Skip if no answers
    if (newAnswers.length === 0) {
      dispatch(
        showNotification({
          message: t('enterValidResponse'),
          type: "error",
        })
      );
      return;
    }

    const questionId = editingData.questionObject?.id;
    if (!questionId) {
      dispatch(
        showNotification({
          message: t('questionIdMissing'),
          type: "error",
        })
      );
      return;
    }

    // Get question type
    const answerType = editingData.type || "text";

    try {
      // Get question option IDs from original data if available
      let questionOptionId;

      if (answerType === "selectmany") {
        // For selectmany, we need an array of option IDs
        const optionIds = editingData.originalData
          ?.map((item: any) => item.questionOptionId)
          .filter(Boolean);

        // If we don't have enough options, or any at all, create placeholders or use just the first one
        if (!optionIds || optionIds.length === 0) {
          questionOptionId = newAnswers.map(() => null);
        } else if (optionIds.length !== newAnswers.length) {
          // If counts don't match, use the available ones and null for the rest
          questionOptionId = [...optionIds];
          while (questionOptionId.length < newAnswers.length) {
            questionOptionId.push(questionOptionId[0] || null);
          }
        } else {
          questionOptionId = optionIds;
        }
      } else {
        // For non-selectmany, use the first option ID if available
        questionOptionId =
          editingData.originalData?.[0]?.questionOptionId || null;
      }

      // Prepare the mutation data
      const mutationData = {
        submissionId: currentSubmission.id,
        questionId: questionId,
        answerType: answerType,
        value: answerType === "selectmany" ? newAnswers : newAnswers[0],
        questionOptionId: questionOptionId,
      };


      // Execute the mutation
      updateAnswerMutation.mutate(mutationData);
    } catch (error: any) {
      console.error("Form validation error:", error);
      dispatch(
        showNotification({
          message: t('checkInputTryAgain'),
          type: "error",
        })
      );
    }
  };

  return (
    <>
      <Modal
        isOpen={showModal}
        onClose={onClose}
        className="flex flex-col gap-5 p-6 rounded-md"
      >
        {!isEditing ? (
          <>
            <div className="flex flex-col gap-4 max-h-[500px] overflow-y-auto">
              <h2 className="text-lg font-semibold text-neutral-700">
                {isMultipleSelection
                  ? t('editSelectedSubmission')
                  : t('editSubmission')}
              </h2>

              <div>
                {/* Add submission selector dropdown when multiple are selected */}
                {isMultipleSelection && selectedSubmissions.length > 0 && (
                  <div className="flex items-center gap-2">
                    <label
                      htmlFor="submission-selector"
                      className="text-sm font-medium text-neutral-700"
                    >
                      {t('selectSubmissionToEdit')}
                    </label>
                    <select
                      id="submission-selector"
                      className="border border-neutral-300 rounded-md p-1 text-sm bg-white"
                      value={selectedSubmissionId || ""}
                      onChange={handleSubmissionChange}
                    >
                      {selectedSubmissions.map((sub) => (
                        <option key={sub.id} value={sub.id}>
                          {t('id')}: {sub.id}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>

            {isMultipleSelection && selectedSubmissionId && (
              <div className="bg-primary-500 border text-neutral-100 rounded-md p-3 text-sm">
                <p className="font-medium">
                 {t('editingSubmissionId')}: {selectedSubmissionId}
                </p>
                <p className="text-xs mt-1">
                  {t('editingOneFromMultiple')}
                </p>
              </div>
            )}

            <p className="text-sm text-neutral-700">
              {isMultipleSelection
                ? t('multipleSelectedChoose')
                : t('editingSingle')}
            </p>

            {/* ShadCN Table */}
            <div className="rounded-md border border-neutral-400 max-h-[450px] overflow-auto">
              <Table>
                <TableHeader className="h-20">
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow
                      className="text-sm border-neutral-400"
                      key={headerGroup.id}
                    >
                      {headerGroup.headers.map((header) => (
                        <TableHead
                          className="py-1 px-6 text-left bg-primary-500 text-neutral-100 font-semibold"
                          key={header.id}
                        >
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row) => (
                      <TableRow
                        className=" text-sm border-neutral-400"
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell className="py-4 px-6" key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={table.getAllColumns().length}
                        className="h-24 text-center"
                      >
                        {t('noResults')}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <button className="btn-outline" onClick={onClose}>
                {t('cancel')}
              </button>
              <button
                className="btn-primary"
                onClick={handleSave}
                disabled={updateAnswerMutation.isPending}
              >
                {updateAnswerMutation.isPending
                  ? t('saving')
                  : t('confirmAndSave')}
              </button>
            </div>
          </>
        ) : (
          <>
            <h2 className="text-lg font-semibold text-neutral-700">
              {t('editingQuestion')}: {editingData?.question}
            </h2>
            <p className="text-sm text-neutral-700">
              {t('editingMultipleInfo')}
            </p>
            <textarea
              value={newResponse}
              onChange={(e) => setNewResponse(e.target.value)}
              className="mt-4 border border-neutral-400 rounded-md p-2 w-full h-24 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder={t('enterNewResponse')}
            />
            <div className="flex justify-end gap-4 mt-6">
              <button
                className="btn-outline"
                onClick={() => {
                  setIsEditing(false);
                  setEditingData(null);
                  setNewResponse("");
                }}
                disabled={updateAnswerMutation.isPending}
              >
                {t('cancel')}
              </button>
              <button
                className="btn-primary"
                onClick={handleSave}
                disabled={updateAnswerMutation.isPending}
              >
                {updateAnswerMutation.isPending
                  ? t('saving')
                  : t('confirmAndSave')}
              </button>
            </div>
          </>
        )}
      </Modal>
    </>
  );
};

export { EditSubmissionModal };
