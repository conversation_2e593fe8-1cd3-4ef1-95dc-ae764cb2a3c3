import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RiQuestionLine } from "react-icons/ri";
import { TbTemplate } from "react-icons/tb";
import { FiUpload } from "react-icons/fi";
import { FaFolder } from "react-icons/fa";
import { hideCreateLibraryModal } from "@/redux/slices/createLibrarySlice";
import { RootState } from "@/redux/store";
import Modal from "./Modal";
import { showCreateLibraryItemModal } from "@/redux/slices/createLibraryItemSlice";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

type ItemOption = {
  id: string;
  title: string;
  icon: React.ElementType;
  onClick: () => void;
};

const CreateLibraryModal = () => {
  const dispatch = useDispatch();
  const isOpen = useSelector((state: RootState) => state.createLibrary.visible);

  // without this closing animation won't work
  const [isClosing, setIsClosing] = useState(false);
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      dispatch(hideCreateLibraryModal());
    }, 300);
    // match the time with animation duration
  };

  const router = useRouter();
  const t = useTranslations();

  const itemOptions: ItemOption[] = [
    {
      id: "question-block",
      title: t('questionBlock'),
      icon: RiQuestionLine,
      onClick: () => {
        handleClose();
        router.push("/library/question-block/form-builder");
      },
    },
    {
      id: "template",
      title: t('templates'),
      icon: TbTemplate,
      onClick: () => {
        handleClose();
        dispatch(showCreateLibraryItemModal("template"));
      },
    },
    {
      id: "upload",
      title: t('upload'),
      icon: FiUpload,
      onClick: () => {
        // Handle Upload
        handleClose();
      },
    },
    {
      id: "collection",
      title: t('collections'),
      icon: FaFolder,
      onClick: () => {
        // Handle Collection creation
        handleClose();
      },
    },
  ];

  return (
    <Modal
      isOpen={isOpen && !isClosing}
      onClose={handleClose}
      className="p-6 rounded-md w-3/5"
    >
      <h2 className="text-lg font-semibold text-neutral-700 mb-4">
        {t('createLibraryItem')}
      </h2>
      <div className="grid grid-cols-2 gap-4">
        {itemOptions.map((option) => (
          <button
            key={option.id}
            onClick={option.onClick}
            className="flex flex-col gap-2 items-center justify-center p-6 bg-neutral-200 rounded-md hover:bg-primary-500 hover:text-neutral-100 cursor-pointer transition-all duration-300"
          >
            <option.icon size={24} className="" />
            <span className="">{option.title}</span>
          </button>
        ))}
      </div>
    </Modal>
  );
};

export { CreateLibraryModal };
