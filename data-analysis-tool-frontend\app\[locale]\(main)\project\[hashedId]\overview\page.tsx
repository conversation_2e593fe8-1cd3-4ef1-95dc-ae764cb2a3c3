"use client";

import {
  AlertCircle,
  Briefcase,
  Calendar,
  ChartGantt,
  CircleHelp,
  Clock,
  Globe,
  Rocket,
  Upload,
  User,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { format } from "date-fns";
import Spinner from "@/components/general/Spinner";
import { Project } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { fetchProjectById } from "@/lib/api/projects";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useAuth } from "@/hooks/useAuth";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useTranslations } from "next-intl";

const ProjectOverviewPage = () => {
  const [hasMounted, setHasMounted] = useState<boolean>(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  const { hashedId } = useParams();

  const hashedIdString = hashedId as string;

  const projectId = decode(hashedIdString);
  const t = useTranslations();

  const { user } = useAuth();

  const dispatch = useDispatch();

  const handleTestForm = () => {
    // Open a new tab with the test form route
    if (hashedId) {
      window.open(`/form-submission/${hashedId}`, "_blank");
    } else {
      console.error("hashedId is missing");
    }
  };

  const handleCopy = () => {
    if (hashedId) {
      const url = `${window.location.origin}/form-submission/${hashedId}`;
      navigator.clipboard.writeText(url)
        .then(() => {
          dispatch(showNotification({ message: t('linkCopied'), type: "success" }));
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
          dispatch(showNotification({ message: t('copyFailed'), type: "error" }));
        });
    } else {
      dispatch(showNotification({ message: t('invalidLink'), type: "warning" }));
    }
  };

  const {
    data: projectData,
    isLoading: projectLoading,
    isError: projectError,
  } = useQuery<Project>({
    queryKey: ["projects", user?.id, projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId && !!user?.id,
  });

  // To prevent errors from showing when the component is not fully mounted.
  if (!hasMounted) return null;

  if (projectLoading) {
    return <Spinner />;
  }

  // If hashedId is missing, show an error
  if (!hashedId || projectId === null) {
    return (
      <div className="error-message">
        <h1 className="text-red-500"> {t('invalidProjectId')} (hashedId).</h1>
        <p className="text-neutral-700">
          {t('invalidProjectUrl')}
        </p>
      </div>
    );
  }

  if (projectError) {
    return (
      <p className="text-red-500"></p>
    );
  }

  return (
    <div className="flex flex-col gap-8">
      {/* Description section */}
      <section className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <span className="text-lg flex items-center gap-2 font-medium capitalize">
            <AlertCircle size={18} /> {t('description')}
          </span>
          <span className="rounded-full px-2 py-1 bg-accent-200 text-accent-700 font-medium text-sm flex items-center gap-2">
            <span className="size-2 rounded-full bg-accent-700"></span>
            {projectData?.status ? t(projectData.status) : t('unknownStatus')}

          </span>
        </div>
        <p>{projectData?.description}</p>
      </section>
      {/* metadata section */}
      <section className="grid grid-cols-3 gap-8">
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2">
          <span className="label-text capitalize">
            <CircleHelp size={16} />
            {t('questions')}
          </span>
          <span className="text-lg font-medium">
            {projectData?.questions?.length}
          </span>
        </div>
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center flex-col gap-2">
          <span className="label-text capitalize">
            <User size={16} />
            {t('owner')}
          </span>
          <span className="text-lg font-medium">{projectData?.user.name}</span>
        </div>
        <div className="rounded-md border-2 border-neutral-400 p-8 flex items-center justify-center text-center flex-col gap-2">
          <span className="label-text capitalize">
            <Briefcase size={16} />
            {t('sector')}
          </span>
          <span className="text-lg font-medium w-full truncate">
            {projectData?.sector}
          </span>
        </div>
        {/* timeline */}
        <div className="flex flex-col gap-4">
          {/* Only show Copy and Open buttons when project status is deployed */}
          {projectData?.status === 'deployed' && (
            <div>
              <span className="text-lg flex items-center gap-2 font-medium capitalize pb-3">
                {t('collectData')}
              </span>
              <div className="flex gap-3">
                <button className="btn-primary" onClick={handleCopy}>
                 {t('copy')}
                </button>
                <button className="btn-primary" onClick={handleTestForm}>
                 {t('open')}
                </button>
              </div>
            </div>
          )}
                <span className="text-lg flex items-center gap-2 font-medium capitalize">
            <ChartGantt size={18} />
            {t('timeline')}
          </span>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Clock size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">{t('lastModified')}</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.updatedAt
                  ? format(new Date(projectData.updatedAt), "MMMM d, yyyy h:mm a")
                  : "N/A"}

              </span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Rocket size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">{t('lastDeployed')}</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.lastDeployedAt
                  ? format(new Date(projectData.lastDeployedAt), "MMMM d, yyyy h:mm a")
                  : t('notDeployedYet')}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* icon */}
            <div className="rounded-full p-2 bg-primary-200">
              <Upload size={16} className="text-primary-500" />
            </div>
            <div>
              <span className="capitalize">{t('latestSubmission')}</span>
              <span className="label-text">
                <Calendar size={16} />
                {projectData?.lastSubmissionAt
                  ? format(
                    new Date(projectData.lastSubmissionAt),
                    "MMMM d, yyyy h:mm a"
                  )
                  : t('noSubmissionsYet')}
              </span>
            </div>
          </div>
        </div>
      </section>
      {/* Country */}
      <section className="label-text cursor-default">
        <Globe size={16} /> {projectData?.country}
      </section>

    </div>
  );
};

export default ProjectOverviewPage;