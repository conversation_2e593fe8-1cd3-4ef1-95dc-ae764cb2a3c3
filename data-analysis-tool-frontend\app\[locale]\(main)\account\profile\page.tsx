"use client";

import { Select } from "@/components/general/Select";
import { Briefcase, Globe, MapPin, Save, User, UserPen } from "lucide-react";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { OrganizationTypeLabelMap } from "@/constants/organizationType";
import { labelToKey } from "@/lib/labelToKey";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Profile } from "@/types";
import { fetchUserProfile, updateUserProfile } from "@/lib/api/users";
import Spinner from "@/components/general/Spinner";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";

const SettingsPage = () => {
  const {
    register,
    formState: { errors, isSubmitting, isSubmitted },
    reset,
    setValue,
    handleSubmit,
  } = useForm();

  useEffect(() => {
    register("country", { required: "Please select a country" });
    register("sector", { required: "Please select a sector" });
    register("organizationType", {
      required: "Please select an organization type",
    });
  }, [register]);

  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);
  const [selectedOrganizationType, setSelectedOrganizationType] = useState<
    string | null
  >(null);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
    setValue("organizationType", selectedOrganizationType, {
      shouldValidate: isSubmitted,
    });
  }, [selectedCountry, selectedSector, selectedOrganizationType, setValue]);

  const { user } = useAuth();
  const t = useTranslations();

  const {
    data: profileData,
    isLoading: profileLoading,
    isError: profileError,
  } = useQuery<Profile>({
    queryKey: ["profile", user?.id],
    queryFn: fetchUserProfile,
    enabled: !!user?.id,
  });

  useEffect(() => {
    if (profileData) {
      reset({
        name: profileData.name || "",
        country: profileData.country || "",
        city: profileData.city || "",
        sector: profileData.sector || "",
        organizationType: profileData.organizationType || "",
        bio: profileData.bio || "",
      });
      setSelectedCountry(profileData.country || null);
      setSelectedSector(profileData.sector || null);
      setSelectedOrganizationType(profileData.organizationType || null);
    }
  }, [profileData, reset]);

  const dispatch = useDispatch();

  const queryClient = useQueryClient();
  const profileMutation = useMutation({
    mutationFn: updateUserProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["profile", user?.id] });
      dispatch(
        showNotification({
          message: t('profileNotificationSuccess'),
          type: "success",
        })
      );
    },
    onError: (error) => {
      dispatch(
        showNotification({
          message:
            t('profileNotificationUnsucess') +
            error.message,
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    profileMutation.mutate({
      dataToSend: {
        name: data.name,
        country: data.country,
        city: data.city,
        sector: data.sector,
        organizationType: data.organizationType,
        bio: data.bio,
      },
    });
  };

  if (profileLoading) {
    return <Spinner />;
  }
  if (profileError) {
    return (
      <p className="text-red-500">
        {t('profileError')}
      </p>
    );
  }
  return (
    <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
      {/* Some text */}
      <div className="flex flex-col gap-2 w-full">
        <h1 className="heading-text">{t('profileInformation')}</h1>
        <p className="sub-text">
          {t('profileUpdateText')}
        </p>
      </div>

      {/* form inputs */}

      <div className="grid grid-cols-1 tablet:grid-cols-2 gap-4">
        {/* Full Name */}
        <div className="label-input-group group">
          <label htmlFor="name" className="label-text">
            <User size={16} /> {t('fullName')}
          </label>
          <input
            {...register("name", { required: "Please enter your name" })}
            id="name"
            type="text"
            className="input-field"
            placeholder="eg: John Doe"
          />
          {errors.name && (
            <p className="text-red-500 text-sm">{`${errors.name.message}`}</p>
          )}
        </div>
        {/* Country */}
        <div className="label-input-group group">
          <label htmlFor="country" className="label-text">
            <Globe size={16} /> {t('country')}
          </label>
          <Select
            id={`country`}
            options={countries}
            value={selectedCountry}
            onChange={setSelectedCountry}
          />
          {errors.country && (
            <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
          )}
        </div>
        {/* City */}
        <div className="label-input-group group">
          <label htmlFor="city" className="label-text">
            <MapPin size={16} /> {t('city')}
          </label>
          <input
            {...register("city")}
            id="city"
            type="text"
            className="input-field"
            placeholder= {'eg: '+  t('kathmandu')}
          />
          {errors.city && (
            <p className="text-red-500 text-sm">{`${errors.city.message}`}</p>
          )}
        </div>
        {/* Sector */}
        <div className="label-input-group group">
          <label htmlFor="sector" className="label-text">
            <Briefcase size={16} /> {t('sector')}
          </label>
          <Select
            id={`sector`}
            options={Object.values(SectorLabelMap)} // Display labels
            value={
              selectedSector && SectorLabelMap[selectedSector]
                ? SectorLabelMap[selectedSector]
                : t('selectOption')
            }
            onChange={(label) => {
              const selectedKey = labelToKey(label, SectorLabelMap);
              setSelectedSector(selectedKey); // Set the enum key for storage
            }}
          />
          {errors.sector && (
            <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
          )}
        </div>
        {/* Organization Type */}
        <div className="label-input-group group">
          <label htmlFor="organizationType" className="label-text">
            <Briefcase size={16} /> {t('organizationType')}
          </label>
          <Select
            id={`organizationType`}
            options={Object.values(OrganizationTypeLabelMap)} // Display organization type labels
            value={
              selectedOrganizationType &&
              OrganizationTypeLabelMap[selectedOrganizationType]
                ? OrganizationTypeLabelMap[selectedOrganizationType]
                : "Select an option"
            }
            onChange={(label) => {
              const selectedKey = labelToKey(label, OrganizationTypeLabelMap);
              setSelectedOrganizationType(selectedKey); // Set the enum key for organization type
            }}
          />
          {errors.organizationType && (
            <p className="text-red-500 text-sm">{`${errors.organizationType.message}`}</p>
          )}
        </div>
        <div className="label-input-group group col-span-2">
          <label htmlFor="bio" className="label-text">
            <UserPen size={16} /> {t('bio')}
          </label>
          <textarea
            {...register("bio")}
            cols={4}
            className="input-field resize-none"
            placeholder={t('bioText')}
          />
        </div>
      </div>
      <button type="submit" className="btn-primary self-end">
        <Save size={18} />
        {isSubmitting ? (
          <span className="flex items-center gap-2">
            Saving{" "}
            <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
          </span>
        ) : (
          <span>{t('save')}</span>
        )}
      </button>
    </form>
  );
};

export default SettingsPage;

/* 



*/
