import { getRequestConfig } from 'next-intl/server';

// Define supported locales
const locales = ['en', 'ne'] as const;
type Locale = typeof locales[number];

// This function loads messages for a specific locale
export async function getMessages(locale: string): Promise<Record<string, any>> {
  // Validate locale
  if (!locales.includes(locale as Locale)) {
    console.warn(`Unsupported locale: ${locale}, falling back to 'en'`);
    locale = 'en';
  }

  try {
    // Dynamic import of the messages file based on locale
    const messages = (await import(`../messages/${locale}.json`)).default;

    if (!messages || typeof messages !== 'object') {
      throw new Error(`Invalid messages format for locale: ${locale}`);
    }

    return messages;
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);

    // Fallback to English if the requested locale fails
    if (locale !== 'en') {
      try {
        console.log('Falling back to English messages');
        return (await import(`../messages/en.json`)).default;
      } catch (fallbackError) {
        console.error('Failed to load fallback English messages', fallbackError);
      }
    }

    // Return empty object as last resort
    return {};
  }
}

// Configuration for next-intl
export default getRequestConfig(async ({ locale }) => {
  // Ensure locale is a valid string
  const localeString = locale?.toString() || 'en';

  return {
    // The locale must be included in the return object
    locale: localeString,
    // Load messages for the locale
    messages: await getMessages(localeString),
    // Set timezone for date formatting
    timeZone: 'Asia/Kathmandu',
    // Default formats for dates, numbers, etc.
    formats: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        medium: {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        },
        long: {
          weekday: 'long',
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'NPR'
        }
      }
    }
  };
});
