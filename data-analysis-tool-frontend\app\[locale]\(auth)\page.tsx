"use client";

import { VerificationModal } from "@/components/modals/VerificationModal";
import { showNotification } from "@/redux/slices/notificationSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { ShieldCheck, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { z } from "zod";
import { useAuth } from "@/hooks/useAuth";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import { useTranslations } from "next-intl";


const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type SignInFormValues = z.infer<typeof signInSchema>;

const page = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
    watch,
  } = useForm<SignInFormValues>({ resolver: zodResolver(signInSchema) });

  // Watch password field to determine when to show the eye button
  const passwordValue = watch("password");

  const router = useRouter();
  const dispatch = useDispatch();

  const t = useTranslations();

  const [showVerificationModal, setShowVerificationModal] =
    useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const { signin } = useAuth({ skipFetchUser: true });

  const onSubmit = async (data: FieldValues) => {
    signin(
      { email: data.email, password: data.password },
      () => {
        dispatch(
          showNotification({ message: t('signInSuccessful'), type: "success" })
        );
        router.push("/dashboard");
      },
      (errorType) => {
        if (errorType === "unverified") {
          setShowVerificationModal(true);
        } else {
          dispatch(
            showNotification({
              message: t('invalidEmailOrPassword'),
              type: "error",
            })
          );
        }
      }
    );
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <VerificationModal
        email={getValues("email")}
        showModal={showVerificationModal}
        setShowModal={setShowVerificationModal}
      />
      <div className="flex flex-col section w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2 mb-8">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            {t('signInToYourAccount')}
          </h1>
          <p className="text-neutral-700 text-center">
            {t('getStarted2')}
          </p>
        </div>
        <form
          className="flex flex-col gap-4 mb-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="group label-input-group">
            <label htmlFor="email" className="label-text">
              {t('email')}
            </label>
            <input
              {...register("email")}
              id="email"
              type="email"
              placeholder={t('enterEmail')}
              className="input-field"
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{`${errors.email.message}`}</p>
            )}
          </div>
          <div className="group label-input-group">
            <label htmlFor="password" className="label-text">
              {t('password')}
            </label>
            <div className="relative">
              <input
                {...register("password")}
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder={t('enterPassword')}
                className="input-field w-full pr-10"
              />
              {passwordValue && passwordValue.length > 0 && (
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide" : "Show"} password
                  </span>
                </button>
              )}
            </div>
            {errors.password && (
              <p className="text-red-500 text-sm">{`${errors.password.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('signingIn')}
                <div className="size-4 rounded-full border-x-2 animate-spin"></div>
              </span>
            ) : (
              t('signIn')
            )}
          </button>
        </form>
        <Link
          href={"/reset-password"}
          className="self-end underline text-neutral-700"
        >
          {t('forgotPassword')}
        </Link>

        <div className="text-neutral-700 flex items-center gap-2">
          <span>{t('noAccount')}</span>
          <Link
            href="/signup"
            className="font-medium hover:text-neutral-900 duration-300"
          >
            {t('signUp')}
          </Link>
        </div>
        <div className="mt-4">
          <LanguageSwitcher/>
        </div>
      </div>
    </div>
  );
};

export default page;
