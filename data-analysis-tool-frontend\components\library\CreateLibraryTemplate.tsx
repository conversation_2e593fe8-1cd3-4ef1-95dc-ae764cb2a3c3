import { createTemplate } from "@/lib/api/templates";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Briefcase, FileText, Globe } from "lucide-react";
import React, { useEffect, useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import { Select } from "../general/Select";
import countries from "@/constants/countryNames.json";
import { SectorLabelMap } from "@/constants/sectors";
import { labelToKey } from "@/lib/labelToKey";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";

const CreateLibraryTemplate = ({
  handleClose,
}: {
  handleClose: () => void;
}) => {
  const dispatch = useDispatch();

  const {
    register,
    formState: { isSubmitting, errors, isSubmitted },
    handleSubmit,
    setValue,
  } = useForm();

  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedSector, setSelectedSector] = useState<string | null>(null);

  useEffect(() => {
    register("country", { required: t('pleaseSelectCountry') });
    register("sector", { required:t('pleaseSelectSector') });
  }, [register]);

  useEffect(() => {
    setValue("country", selectedCountry, { shouldValidate: isSubmitted });
    setValue("sector", selectedSector, { shouldValidate: isSubmitted });
  }, [setValue, selectedCountry, selectedSector]);

  const queryClient = useQueryClient();

  const { user } = useAuth();
  const t = useTranslations();

  const templateMutation = useMutation({
    mutationFn: createTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates", user?.id] });
      handleClose();
      dispatch(
        showNotification({
          type: "success",
          message: t('templateCreated'),
        })
      );
    },
    onError: () => {
      dispatch(
        showNotification({
          type: "error",
          message: t('templateCreationFailed'),
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    const dataToSend = {
      name: data.name,
      description: data.description,
      sector: data.sector,
      country: data.country,
    };

    templateMutation.mutate(dataToSend);
  };

  return (
    <form className="flex flex-col gap-8" onSubmit={handleSubmit(onSubmit)}>
      <h1 className="heading-text capitalize">{t('createNewProjectTemplate')}</h1>
      <div className="flex flex-col gap-4">
        {/* Project Name */}
        <div className="label-input-group group">
          <label htmlFor="project-name" className="label-text">
            <FileText size={16} /> {t('templateName')}
          </label>
          <input
            {...register("name", {
              required: t('templateNameRequired'),
            })}
            id="project-name"
            type="text"
            className="input-field"
            placeholder={t('enterProjectName')}
          />
          {errors.name && (
            <p className="text-red-500 text-sm">{`${errors.name.message}`}</p>
          )}
        </div>
        {/* Project Description */}
        <div className="label-input-group group">
          <label htmlFor="description" className="label-text">
            {t('description')}
          </label>
          <textarea
            id="description"
            {...register("description", {
              required: t('enterTemplateDescription'),
            })}
            className="input-field resize-none"
            cols={4}
            placeholder={t('enterProjectDescription')}
          />
          {errors.description && (
            <p className="text-red-500 text-sm">{`${errors.description.message}`}</p>
          )}
        </div>
        {/* Country and Sector */}
        <div className="grid grid-cols-2 gap-4">
          <div className="label-input-group group">
            <label htmlFor="country" className="label-text">
              <Globe size={16} />
              {t('country')}
            </label>
            <Select
              id={`country`}
              options={countries}
              value={selectedCountry || t('selectOption')}
              onChange={setSelectedCountry}
            />
            {errors.country && (
              <p className="text-red-500 text-sm">{`${errors.country.message}`}</p>
            )}
          </div>
          <div className="label-input-group group">
            <label htmlFor="sector" className="label-text">
              <Briefcase size={16} /> {t('sector')}
            </label>
            <Select
              id={`sector`}
              options={Object.values(SectorLabelMap)} // Display labels
              value={
                selectedSector && SectorLabelMap[selectedSector]
                  ? SectorLabelMap[selectedSector]
                  : t('selectOption')
              }
              onChange={(label) => {
                const selectedKey = labelToKey(label, SectorLabelMap);
                setSelectedSector(selectedKey); // Set the enum key for storage
              }}
            />
            {errors.sector && (
              <p className="text-red-500 text-sm">{`${errors.sector.message}`}</p>
            )}
          </div>
        </div>
        <button type="submit" className="btn-primary self-end">
          {isSubmitting ? (
            <span className="flex items-center gap-2">
              {t('creating')}
              <div className="size-4 animate-spin border-x border-neutral-100 rounded-full"></div>
            </span>
          ) : (
            t('createProject')
          )}
        </button>
      </div>
    </form>
  );
};

export { CreateLibraryTemplate };
