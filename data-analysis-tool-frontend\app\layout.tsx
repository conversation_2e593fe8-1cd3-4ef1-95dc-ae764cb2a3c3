import { Metadata } from "next";
import "./globals.css";
import { <PERSON><PERSON><PERSON>, Noto_Sans_Devanagari } from "next/font/google";
import { ReduxProvider } from "@/providers/ReduxProvider";
import { Notification } from "@/components/general/Notification";
import { ReactQueryProvider } from "@/providers/ReactQueryProvider";

const poppins = Poppins({
  weight: ["300", "400", "500", "600"],
  subsets: ["latin"],
  display: "swap",
});

const devanagari = Noto_Sans_Devanagari({
  weight: ["400", "700"],
  subsets: ["devanagari"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Data analysis tool",
  description: "A tool for data collection and analysis.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${poppins.className} ${devanagari.className} antialiased`}
      >
        <ReduxProvider>
          <ReactQueryProvider>
            <Notification />
            <main className="bg-neutral-200">{children}</main>
          </ReactQueryProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
