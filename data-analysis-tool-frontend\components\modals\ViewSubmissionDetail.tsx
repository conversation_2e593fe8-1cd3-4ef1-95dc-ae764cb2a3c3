"use client";

import React, { useRef, useState } from "react";
import Modal from "./Modal";
import { Submission } from "@/app/[locale]/(main)/project/[hashedId]/data/table/columns";
import { LuFullscreen } from "react-icons/lu";
import { useTranslations } from "next-intl";

const ViewSubmissionDetail = ({
  isOpen,
  onClose,
  submission,
}: {
  isOpen: boolean;
  onClose: () => void;
  submission: Submission;
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const t = useTranslations();

  // Ref to the container that will go fullscreen
  const contentRef = useRef<HTMLDivElement>(null);
  const handleFullscreenToggle = () => {
    setIsFullscreen((prev) => !prev);
  };

  // Group answers by question label
  const groupedAnswers = new Map<string, (string | number)[]>();
  submission.answers.forEach((answer) => {
    const label = answer.question.label;
    if (!groupedAnswers.has(label)) {
      groupedAnswers.set(label, []);
    }
    groupedAnswers.get(label)!.push(answer.value);
  });

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="p-6 rounded-md max-w-4xl w-full "
    >
      <div
        ref={contentRef}
        className={`flex flex-col gap-4 transition-all duration-300 ${
          isFullscreen
            ? "fixed inset-0 bg-neutral-100 w-screen h-screen z-50 p-6 overflow-auto"
            : ""
        }`}
      >
        <div className="flex flex-col gap-4">
          <h2 className="text-lg font-semibold text-neutral-700">
            {t('submissionDetails')}
          </h2>
          <div className="flex items-center gap-2">
            <span className="text-sm text-neutral-600">Validation status:</span>
            <select className="px-3 py-1 border border-neutral-500 rounded-md text-sm">
              <option value="">{t('select')}</option>
              <option value="valid">{t('valid')}</option>
              <option value="invalid">{t('notValid')}</option>
              <option value="pending">{t('pending')}</option>
            </select>
            <button onClick={handleFullscreenToggle} className="btn-primary">
              <LuFullscreen className="w-5 h-5" />
              {isFullscreen ? t('exitFullscreen') : t('fullscreen')}
            </button>
          </div>
        </div>

        <div className="overflow-x-auto rounded-md border border-neutral-200 bg-neutral-100">
          <table className="min-w-full divide-y divide-neutral-200">
            <thead className="bg-primary-500 text-neutral-100">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium  uppercase tracking-wider">
                  {t('question')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider">
                  {t('response')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-neutral-100 divide-y divide-neutral-200">
              {[...groupedAnswers.entries()].map(([label, values]) => {
                // Find the corresponding answer to get question details
                const answer = submission.answers.find(
                  (a) => a.question.label === label
                );
                const isTableInput = answer?.question.inputType === "table";

                return (
                  <tr key={label}>
                    <td className="px-4 py-2 align-top">{label}</td>
                    <td className="px-4 py-2">
                      {isTableInput ? (
                        // Show simple text for table input types
                        <div className="text-neutral-600 italic">
                          {t('tableDataNote')}
                        </div>
                      ) : (
                        // Show regular values for non-table input types
                        values.join(", ")
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-neutral-600 font-semibold">
            <p>{t('submittedBy')}: {submission.user?.name}</p>
            <p>{t('submissionTime')}: {submission.submissionTime}</p>
          </div>
          <div>
            <button className="btn-primary" onClick={onClose}>
              {t('close')}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ViewSubmissionDetail;
