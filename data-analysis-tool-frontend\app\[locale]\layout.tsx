
import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { getMessages } from '@/lib/get-messages';

// Define supported locales
const locales = ['en', 'ne'] as const;
type Locale = typeof locales[number];

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // Await the params before accessing its properties
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  const isValidLocale = locales.includes(locale as Locale);
  if (!isValidLocale) notFound();

  // Load messages for the locale
  const messages = await getMessages(locale);

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      {children}
    </NextIntlClientProvider>
  );
}
