"use client";

import React, { useState } from "react";
import Modal from "./Modal";
import { LuLayoutTemplate } from "react-icons/lu";
import { MdEdit } from "react-icons/md";
import { useDispatch } from "react-redux";
import { showCreateProjectModal } from "@/redux/slices/createProjectSlice";
import { ChooseTemplateModal } from "./ChooseTemplateModal";
import { useTranslations } from "next-intl";

interface NewProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewProjectModal: React.FC<NewProjectModalProps> = ({
  isOpen,
  onClose,
}) => {
  const dispatch = useDispatch();
  const t = useTranslations();
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  const handleCreateProject = () => {
    // Close current modal first
    onClose();
    // Then show the CreateProjectModal via Redux
    dispatch(showCreateProjectModal());
  };

  const handleUseTemplate = () => {
    setShowTemplateModal(true);
  };

  const handleChooseTemplateModalClose = (action: "back" | "close") => {
    setShowTemplateModal(false);
    if (action === "close") {
      onClose();
    }
  };

  return (
    <>
      <Modal
        isOpen={isOpen && !showTemplateModal}
        onClose={onClose}
        className="bg-neutral-100 p-4 mobile:p-6 rounded-lg w-[95%] mobile:w-[85%] tablet:w-[75%] laptop:w-[60%] desktop:w-[50%] max-w-3xl mx-auto"
      >
        <div className="flex flex-col gap-4 mobile:gap-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg mobile:text-xl font-semibold text-neutral-700">
              {t('createProjectChooseSource')}
            </h2>
          </div>

          <p className="text-sm mobile:text-base text-neutral-600">
            {t('chooseOptionToContinue')}
          </p>

          <div className="grid grid-cols-1 mobile:grid-cols-2 gap-3 mobile:gap-4">
            {/* Build from scratch */}
            <div
              onClick={handleCreateProject}
              className="bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300"
            >
              <MdEdit className="w-5 h-5 mobile:w-6 mobile:h-6" />
              <span className="text-sm mobile:text-base text-center">
                {t('buildFromScratch')}
              </span>
            </div>

            {/* Use a template */}
            <div
              onClick={handleUseTemplate}
              className="bg-neutral-200 rounded-lg p-4 mobile:p-6 tablet:p-8 flex flex-col items-center justify-center gap-2 mobile:gap-3 cursor-pointer hover:bg-primary-500 hover:text-neutral-100 transition-all duration-300"
            >
              <LuLayoutTemplate className="w-5 h-5 mobile:w-6 mobile:h-6" />
              <span className="text-sm mobile:text-base text-center">
                {t('useTemplate')}
              </span>
            </div>

            {/* Upload an XLSForm */}
            {/* <div className="bg-neutral-100 rounded-lg p-4 sm:p-6 md:p-8 flex flex-col items-center justify-center gap-2 sm:gap-3 cursor-pointer hover:bg-neutral-200 transition-all duration-300">
              <LuUpload className="w-5 h-5 sm:w-6 sm:h-6 text-neutral-600" />
              <span className="text-sm sm:text-base text-neutral-700 text-center">Upload an XLSForm</span>
            </div> */}

            {/* Import an XLSForm via URL */}
            {/* <div className="bg-neutral-100 rounded-lg p-4 sm:p-6 md:p-8 flex flex-col items-center justify-center gap-2 sm:gap-3 cursor-pointer hover:bg-neutral-200 transition-all duration-300">
              <LuLink className="w-5 h-5 sm:w-6 sm:h-6 text-neutral-600" />
              <span className="text-sm sm:text-base text-neutral-700 text-center">Import an XLSForm via URL</span>
            </div> */}
          </div>
        </div>
      </Modal>

      {/* Template selection modal */}

      <ChooseTemplateModal
        isOpen={showTemplateModal}
        onClose={() => handleChooseTemplateModalClose("close")}
        back={() => handleChooseTemplateModalClose("back")}
      />
    </>
  );
};

export { NewProjectModal };