"use client";

import { Session } from "@/types";
import { ColumnDef } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { formatDate } from "@/lib/utils";

// Function to format cell value based on data type
const formatCellValue = (value: any, type?: string, t?: any): string => {
  if (value === null || value === undefined) return "-";

  if (typeof value === "boolean") {
    return value ? (t ? t("yes") : "Yes") : (t ? t("no") : "No");
  }

  if (value instanceof Date) {
    return formatDate(value);
  }

  if (type === "date" && typeof value === "string") {
    try {
      return formatDate(new Date(value));
    } catch {
      return value;
    }
  }

  return String(value);
};

// Create a hook that returns the columns with translations
export const securityColumns = (): ColumnDef<Session>[] => {
  const t = useTranslations();

  return [
    {
      accessorKey: "deviceInfo",
      header: t("device"),
    },
    {
      accessorKey: "browserInfo",
      header: t("browser"),
    },
    {
      accessorKey: "updatedAt",
      header: t("last_activity"),
      cell: ({ getValue }) => {
        const value = getValue();
        return (
          <div className="font-medium text-neutral-700">
            {formatCellValue(value, "date", t) || t("notRecorded")}
          </div>
        );
      },
    },
    {
      accessorKey: "ipAddress",
      header: t("ip_address"),
    },
    {
      accessorKey: "isActive",
      header: t("status"),
      cell: ({ getValue }) => {
        const value = getValue() as boolean;
        return value ? t("active") : t("inactive");
      },
    },
  ];
};