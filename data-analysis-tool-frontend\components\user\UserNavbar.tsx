import { ShieldAlert, User, Database } from "lucide-react";
import React from "react";
import { Navbar } from "../general/SecondaryNavbar";
import { useTranslations } from "next-intl";

const UserNavbar = () => {
  const t = useTranslations();
  const userNavbarItems = [
    {
      label: t('profile'),
      icon: <User size={16} />,
      route: "/account/profile",
    },
    {
      label: t('security'),
      icon: <ShieldAlert size={16} />,
      route: "/account/security",
    },
  ];

  return <Navbar items={userNavbarItems} />;
};

export { UserNavbar };
