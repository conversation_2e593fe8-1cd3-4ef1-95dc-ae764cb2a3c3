import React, { useState, useEffect } from "react";
import Modal from "./Modal";
import { LuPlus } from "react-icons/lu";
import { Project } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { fetchProjectById } from "@/lib/api/projects";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { useAuth } from "@/hooks/useAuth";
import Spinner from "../general/Spinner";
import { AddUser } from "../addUser/AddUser";
import axios from "@/lib/axios";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

const ShareProjectModal = ({
  showModal,
  onClose,
  onShare,
  selectedProject,
}: {
  showModal: boolean;
  onClose: () => void;
  onShare: () => void;
  selectedProject?: Project;
  selectedProjects?: Project[];
}) => {
  const { hashedId } = useParams();
  const { user } = useAuth();
  const [showAddUser, setShowAddUser] = useState(false);

  // Get project ID from either selected project or URL
  const urlProjectId = hashedId ? decode(hashedId as string) : null;
  const projectId = selectedProject?.id || urlProjectId;
  const t = useTranslations();

  // Fetch project details using the project ID
  const { data: projectData, isLoading: projectLoading } = useQuery<Project>({
    queryKey: ["project", projectId],
    queryFn: async () => {
      const data = await fetchProjectById({ projectId: projectId! });
      return data;
    },
    enabled: !!projectId && !!user?.id,
  });

  // Fetch project users
  const [projectUsers, setProjectUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      if (!projectId) return;

      setUsersLoading(true);
      try {
        const response = await axios.get(`/project-users/${projectId}`);

        if (response.data && response.data.data && response.data.data.AllUser) {
          const users = response.data.data.AllUser || [];
          setProjectUsers(users);
        } else {
          console.warn("No users data in response:", response.data);
          setProjectUsers([]);
        }
      } catch (error) {
        console.error("Error fetching project users:", error);
        setProjectUsers([]);
      } finally {
        setUsersLoading(false);
      }
    };

    if (showModal && projectId) {
      fetchUsers();
    }
  }, [projectId, showModal]);

  // If loading, show spinner
  if (projectLoading) {
    return <Spinner />;
  }

  // Use the fetched project data or fallback to selected project
  const displayData = projectData || selectedProject;

  // If we have no data at all, show error
  if (!displayData) {
    return (
      <Modal isOpen={showModal} onClose={onClose} className="p-6 rounded-md">
        <div className="text-center py-4">
          <p className="text-red-500">{t('projectNotFound')}</p>
          <Button onClick={onClose} className="mt-4">
            {t('close')}
          </Button>
        </div>
      </Modal>
    );
  }

  // Generate avatar color based on user name
  const getAvatarColor = (name?: string) => {
    if (!name) return "bg-gray-500"; // Default color if no name provided
    const colors = [
      "bg-green-500",
      "bg-blue-500",
      "bg-red-500",
      "bg-purple-500",
      "bg-yellow-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-orange-500",
    ];
    const charCode = name.charCodeAt(0);
    return colors[charCode % colors.length];
  };

  // Get the first letter of the name for the avatar
  const getInitial = (name?: string) => {
    return name ? name.charAt(0).toUpperCase() : "?";
  };

  return (
    <Modal isOpen={showModal} onClose={onClose} className="p-6 rounded-md">
      <h2 className="text-lg font-semibold text-neutral-700">
        {`${t('sharingProject')}: ${displayData.name || ""}`}
      </h2>

      <div className="w-2xl mt-4 p-4 max-h-[500px] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="text-xl font-semibold">{t('whoHasAccess')}</div>
          <div
            className="flex items-center border rounded-md px-3 py-1.5 cursor-pointer hover:bg-gray-50"
            onClick={() => setShowAddUser(true)}
          >
            <LuPlus size={18} className="mr-2" />
            <div className="text-sm">{t('addUser')}</div>
          </div>
        </div>

        {/* User List */}
        <div className="space-y-4">
          {/* Project Owner */}
          {displayData.user && (
            <div className="flex items-center">
              <div
                className={`w-10 h-10 rounded-full ${getAvatarColor(
                  displayData.user.name
                )} flex items-center justify-center text-neutral-100 font-medium mr-3`}
              >
                {getInitial(displayData.user.name)}
              </div>
              <div className="flex-1">
                <div className="font-medium">
                  {displayData.user.name ||
                    displayData.user.email ||
                    t('unknownUser')}
                </div>
                <div className="inline-block bg-gray-100 text-xs px-2 py-0.5 rounded">
                  {t('owner')}
                </div>
              </div>
            </div>
          )}

          {/* Project Users */}
          {usersLoading ? (
            <div className="py-2 text-center">
              <div className="inline-block w-6 h-6 rounded-full border-2 border-t-transparent border-primary-500 animate-spin"></div>
            </div>
          ) : projectUsers && projectUsers.length > 0 ? (
            projectUsers.map((projectUser: any, index: number) => {
              const userName =
                (projectUser.user && projectUser.user.name) ||
                (projectUser.user && projectUser.user.email) ||
                `User ${projectUser.userId}`;

              return (
                <div key={index} className="flex items-center mt-4">
                  <div
                    className={`w-10 h-10 rounded-full ${getAvatarColor(
                      userName
                    )} flex items-center justify-center text-neutral-100 font-medium mr-3`}
                  >
                    {getInitial(userName)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{userName}</div>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {projectUser.permission &&
                        Object.entries(projectUser.permission)
                          .filter(([key, value]) => value === true)
                          .map(([key]) => (
                            <div
                              key={key}
                              className="inline-block bg-gray-100 text-xs px-2 py-0.5 rounded"
                            >
                              {key === "viewForm"
                                ? t('viewForm')
                                : key === "editForm"
                                ? t('editForm')
                                : key === "viewSubmissions"
                                ? t('viewSubmissions')
                                : key === "editSubmissions"
                                ? t('editSubmissions')
                                : key === "addSubmissions"
                                ? t('addSubmissions')
                                : key === "deleteSubmissions"
                                ? t('deleteSubmissions')
                                : key === "validateSubmissions"
                                ? t('validateSubmissions')
                                : key === "manageProject"
                                ? t('manageProject')
                                : key}
                            </div>
                          ))}
                    </div>
                  </div>
                </div>
              );
            })
          ) : null}
        </div>

        {/* AddUser form */}
        {showAddUser && projectId && (
          <div className="mt-6">
            <AddUser
              onClose={() => setShowAddUser(false)}
              projectId={projectId}
              onUserAdded={() => {
                // Refetch users when a new user is added
                const fetchUsers = async () => {
                  setUsersLoading(true);
                  try {
                    const response = await axios.get(
                      `/project-users/${projectId}`
                    );
                    if (
                      response.data &&
                      response.data.data &&
                      response.data.data.AllUser
                    ) {
                      const users = response.data.data.AllUser || [];
                      setProjectUsers(users);
                    } else {
                      setProjectUsers([]);
                    }
                  } catch (error) {
                    console.error("Error fetching project users:", error);
                    setProjectUsers([]);
                  } finally {
                    setUsersLoading(false);
                  }
                };
                fetchUsers();
              }}
            />
          </div>
        )}

        {/* Anonymous Submissions */}
        <div className="mt-8 border-t pt-6">
          <div className="flex justify-between items-center">
            <div>
              <div className="font-medium">{t('anonymousSubmissions')}</div>
              <div className="text-sm text-gray-500 mt-1">
                {t('allowAnonymousSubmissions')}
              </div>
            </div>
            <div className="w-12 h-6 bg-gray-200 rounded-full relative cursor-pointer">
              <div className="w-5 h-5 bg-neutral-100 rounded-full absolute top-0.5 left-0.5 shadow"></div>
            </div>
          </div>
        </div>

        {/* Copy Team Button */}
        <div className="mt-8">
          <div className="inline-block border rounded-md px-4 py-2 text-sm cursor-pointer hover:bg-gray-50">
            {t('copyTeamFromProject')}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export { ShareProjectModal };
