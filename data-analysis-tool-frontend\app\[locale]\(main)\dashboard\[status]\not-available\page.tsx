"use client";

import React from "react";
import { useParams } from "next/navigation";
import { BsFolderPlus } from "react-icons/bs";
import { useDispatch } from "react-redux";
import { showCreateProjectModal } from "@/redux/slices/createProjectSlice";
import { useTranslations } from "next-intl";

const NotAvailablePage = () => {
  const { status } = useParams();
  const dispatch = useDispatch();
  const t = useTranslations();
  
  const getStatusTranslated = () => {
    if (!status || typeof status !== 'string') return t('projects');
    return t(`status2.${status}`);
  };

  const handleCreateProject = () => {
    dispatch(showCreateProjectModal());
  };

  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 min-h-[70vh]">
      <div className="bg-neutral-100 rounded-lg shadow-sm p-8 max-w-md w-full text-center">
        <div className="flex justify-center mb-6">
          <div className="bg-neutral-200 p-5 rounded-full">
            <BsFolderPlus size={50} className="text-primary-500" />
          </div>
        </div>
        <h2 className="text-2xl font-semibold text-neutral-800 mb-2">
  {t('noProjects', { status: getStatusTranslated() })}
</h2>
        <p className="text-neutral-600 mb-8">
          {status === "draft" && t('noDraftProjects')}
          {status === "deployed" && t('noDeployedProjects')}
          {status === "archived" && t('noArchivedProjects')}
          {!status && t('noProjectsInCategory')}
        </p>
        
        {status === "draft" && (
          <button 
            onClick={handleCreateProject}
            className="btn-primary w-full"
          >
            {t('createNewProject')}
          </button>
        )}
        
        {status === "deployed" && (
          <div className="text-sm text-neutral-500">
            {t('createAndDeployProject')}
          </div>
        )}
        
        {status === "archived" && (
          <div className="text-sm text-neutral-500">
            {t('archiveProjectsInfo')}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotAvailablePage; 