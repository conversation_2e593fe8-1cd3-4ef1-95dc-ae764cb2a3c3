"use client";

import { ResetLinkSentModal } from "@/components/modals/ResetLinkSentModal";
import { ArrowLef<PERSON>, ArrowRight, ShieldCheck } from "lucide-react";
import Link from "next/link";
import React, { useState } from "react";
import { FieldValues, useForm } from "react-hook-form";
import axios from "@/lib/axios";
import { useTranslations } from "next-intl";
import LanguageSwitcher from "@/components/LanguageSwitcher";

const PasswordResetPage = () => {
  const {
    register,
    formState: { errors, isSubmitting },
    handleSubmit,
    getValues,
  } = useForm();

  const [showResetLinkSentModal, setShowResetLinkShowModal] =
    useState<boolean>(false);

  const t = useTranslations();

  const onSubmit = async (data: FieldValues) => {
    try {
      await axios.post(`/users/forgetpassword`, { email: data.email });
      setShowResetLinkShowModal(true);
    } catch (error) {
      console.error(error instanceof Error ? error.message : error);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <ResetLinkSentModal
        email={getValues("email")}
        showModal={showResetLinkSentModal}
        setShowModal={setShowResetLinkShowModal}
      />
      <div className="section flex flex-col gap-8 w-11/12 mobile:w-4/5 tablet:w-lg">
        <div className="flex flex-col items-center gap-2">
          <ShieldCheck size={36} />
          <h1 className="text-2xl tablet:text-3xl font-semibold text-center">
            {t('resetYourPassword')}
          </h1>
          <p className="text-neutral-700 text-center">
            {t('resetPasswordInstructions')}
          </p>
        </div>
        <form
          className="flex flex-col gap-4 "
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          <div className="label-input-group group">
            <label htmlFor="email" className="label-text">
              {t('email')}
            </label>
            <input
              {...register("email", { required: t('pleaseEnterYourEmail') })}
              id="email"
              type="email"
              className="input-field"
              placeholder="eg: <EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-red-500">{`${errors.email.message}`}</p>
            )}
          </div>
          <button type="submit" className="btn-primary">
            {isSubmitting ? (
              <span className="flex items-center gap-2">
                {t('sending')}
                <div className="animate-spin border-x-2 border-neutral-100 rounded-full size-4"></div>
              </span>
            ) : (
              <span className="flex items-center gap-2">{t('sendResetLink')}</span>
            )}
          </button>
        </form>
        <Link
          href="/"
          className="text-neutral-700 self-center flex items-center gap-2"
        >
          <ArrowLeft size={16} /> {t('backToSignin')}
        </Link>
          <LanguageSwitcher />
      </div>
    </div>
  );
};

export default PasswordResetPage;
