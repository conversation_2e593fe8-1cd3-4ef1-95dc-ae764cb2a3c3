"use client";

import { exportData, getexportData, downloadExportData } from "@/lib/api/export";
import { decode } from "@/lib/encodeDecode";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { LuFiles } from "react-icons/lu";

import { Download, Filter, RefreshCw, Search } from "lucide-react";
import { useParams } from "next/navigation";
import React, { useState } from "react";
import { useTranslations } from "next-intl";

type fileType = {
  id: number;
  fileName: string;
  fileType: string;
  createdAt: string;
};

export default function DownloadsPage() {
  const dispatch = useDispatch();

  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [type, setType] = useState("");

  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const projectId = decode(hashedIdString);
  const t = useTranslations();

  const [downloadingFileId, setDownloadingFileId] = useState<number | null>(null);


  const formatDate = (dateStr: string) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(dateStr));
  };

  const exportDataMutation = useMutation({
    mutationFn: () => exportData([projectId!], type),
    onSuccess: () => {
      dispatch(
        showNotification({
          message: t('exportedSuccessfully'),
          type: "success",
        })
      );
      // Invalidate the query to refetch updated data
      queryClient.invalidateQueries({
        queryKey: ["exportData", projectId],
      });
    },
    onError: (error) => {
      dispatch(
        showNotification({
          message: t('errorExporting'),
          type: "error",
        })
      );
    },
  });

  const handleDownload = async (fileId: number, fileName: string) => {
    setDownloadingFileId(fileId);
    try {
      if (fileId !== null && fileName) {
        await downloadExportData(fileId.toString(), fileName);
      } else {
        dispatch(showNotification({ message: t('noFileSelected'), type: "error" }));
      }
    } catch (error) {
      dispatch(showNotification({ message: t('downloadFailed'), type: "error" }));
    } finally {
      setDownloadingFileId(null);
    }
  };
  
  

  const { data: exportDataResponse, isLoading: isExportDataLoading } = useQuery(
    {
      queryKey: ["exportData", projectId],
      queryFn: () => getexportData([projectId!]),
      enabled: !!projectId,
    }
  );

  const handleExport = () => {
    exportDataMutation.mutate();
  };

  

  return (
    <div className="flex flex-col space-y-6">
      {/* Header with title and action buttons */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-neutral-800">{t('downloads')}</h1>
        <div className="flex gap-2">
          <button className="btn-primary" title={t('refreshDownloads')}>
            <RefreshCw className="w-4 h-4" />
            <span>{t('refresh')}</span>
          </button>
        </div>
      </div>

      {/* Export options and search */}
      <div className="flex justify-between gap-4 flex-wrap">
        <div className="flex gap-2">
          <select
            value={type}
            onChange={(e) => setType(e.target.value)}
            className="border border-neutral-300 rounded px-3 py-2 bg-neutral-100 text-neutral-800 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors cursor-pointer"
          >
            <option>{t('exportFormat')}</option>
            <option>csv</option>
            <option>excel</option>
          </select>
          <button onClick={handleExport} className="btn-primary">
            <Download className="w-4 h-4" />
            <span>{t('export')}</span>
          </button>
          <button className="border btn-primary">
            <Filter className="w-4 h-4" />
            <span>{t('filter')}</span>
          </button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder={t('searchFiles')}
            className="pl-9 pr-4 py-2 border border-neutral-300 rounded w-64 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 focus:outline-none transition-colors"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 w-4 h-4 text-neutral-400" />
        </div>
      </div>

      {/* Files list */}
      <div className="bg-neutral-100 border border-neutral-200 rounded-md overflow-hidden shadow-sm">
        <div className="grid grid-cols-12 py-2 px-4 bg-primary-50 border-b border-neutral-200">
          <div className="col-span-6 text-xs font-medium text-primary-700 uppercase">
            {t('name')}
          </div>
          <div className="col-span-2 text-xs font-medium text-primary-700 uppercase">
            {t('type')}
          </div>

          <div className="col-span-2 text-xs font-medium text-primary-700 uppercase">
            {t('date')}
          </div>
        </div>

        <ul className="divide-y divide-neutral-200">
          {exportDataResponse?.data?.files?.map((file: fileType) => {
            return (
              <li
                key={file.id}
                className="grid grid-cols-12 py-3 px-4 hover:bg-primary-50 items-center"
              >
                <div className="col-span-6 flex items-center gap-3">
                  <LuFiles className="w-5 h-5 text-neutral-400" />
                  <span className="font-medium text-neutral-800">
                    {file.fileName}
                  </span>
                </div>
                <div className="col-span-2 text-sm text-neutral-600 uppercase">
                  {file.fileType}
                </div>

                <div className="col-span-1 text-sm text-neutral-600">
                  {formatDate(file.createdAt)}
                </div>
                <div className="col-span-1">
                  <button
                  onClick={() => {
                    handleDownload(file.id, file.fileName);
                  }}
                    className="p-1 rounded-full hover:bg-primary-500 hover:text-neutral-100 text-neutral-700 transition-colors cursor-pointer"
                    title={t('downloadFile')}
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
}
