"use client";

import React from "react";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";

interface DeleteQuestionGroupModalProps {
  showModal: boolean;
  setShowModal: (show: boolean) => void;
  onConfirmDelete: () => void;
  onConfirmDeleteWithQuestions: () => void;
  isDeleting: boolean;
}

const DeleteQuestionGroupModal = ({
  showModal,
  setShowModal,
  onConfirmDelete,
  onConfirmDeleteWithQuestions,
  isDeleting,
}: DeleteQuestionGroupModalProps) => {
  const t = useTranslations();
  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">{t('deleteQuestionGroup')}</h2>
          <button
            onClick={() => setShowModal(false)}
            className="text-gray-500 hover:text-gray-700"
            disabled={isDeleting}
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <p className="mb-4">
            {t('deleteQuestionGroupPrompt')}
          </p>

          <div className="space-y-4">
            <button
              onClick={onConfirmDelete}
              className="w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600"
              disabled={isDeleting}
            >
              {isDeleting ? t('deleting') : t('deleteGroupOnly')}
            </button>
            <p className="text-sm text-gray-600 mb-4">
              {t('deleteGroupKeepQuestions')}
            </p>

            <button
              onClick={onConfirmDeleteWithQuestions}
              className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              disabled={isDeleting}
            >
              {isDeleting ? t('deleting') : t('deleteGroupQuestions')}
            </button>
            <p className="text-sm text-gray-600 mb-4">
              {t('deleteGroupAndQuestions')}
            </p>

            <button
              onClick={() => setShowModal(false)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isDeleting}
            >
              {t('cancel')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export { DeleteQuestionGroupModal };
