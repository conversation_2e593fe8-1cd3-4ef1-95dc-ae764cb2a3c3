import { InputTypeMap, InputType } from "@/constants/inputType";
import { needsOptions } from "@/lib/needsOptions";
import { z } from "zod";

export type QuestionOption = {
  id: number;
  label: string;
  sublabel: string;
  code: string;
  nextQuestionId?: number | null;
};

export type Question = {
  id: number;
  label: string;
  inputType: InputType;
  hint?: string;
  placeholder?: string;
  isRequired: boolean;
  position: number;
  questionOptions: QuestionOption[];
  questionGroupId?: number;
};

export type QuestionGroup = {
  id: number;
  title: string;
  order: number;
  projectId: number;
  parentGroupId?: number;
  parentGroup?: QuestionGroup;
  subGroups?: QuestionGroup[];
  createdAt?: string;
  updatedAt?: string;
  question?: Question[];
};

// schema for validating question
export const InputTypeKeys = Object.keys(
  InputTypeMap
) as (keyof typeof InputTypeMap)[];

export const QuestionSchema = z
  .object({
    label: z.string().min(1, "Question name is required"),
    inputType: z.enum(InputTypeKeys as [string, ...string[]]),
    hint: z.string().optional(),
    placeholder: z.string().optional(),
    questionOptions: z
      .array(
        z.object({
          label: z.string(),
          sublabel: z.string().optional(),
          code: z.string(),
          nextQuestionId: z.number().optional().nullable(),
        })
      )
      .optional(),
  })
  .superRefine((data, ctx) => {
    const needsOpts = needsOptions(data.inputType);

    // Only validate questionOptions if using manual entry (not file upload)
    // This will be checked separately in the form submission
    // if (needsOpts) {
    //   if (!data.questionOptions || data.questionOptions.length === 0) {
    //     ctx.addIssue({
    //       path: ["questionOptions"],
    //       code: z.ZodIssueCode.custom,
    //       message: "At least one option is required for this input type",
    //     });
    //   } else {
    //     // Validate each option has required fields
    //     data.questionOptions.forEach((option, index) => {
    //       if (!option.label.trim()) {
    //         ctx.addIssue({
    //           path: ["questionOptions", index, "label"],
    //           code: z.ZodIssueCode.custom,
    //           message: "Option label is required",
    //         });
    //       }
    //       if (!option.code.trim()) {
    //         ctx.addIssue({
    //           path: ["questionOptions", index, "code"],
    //           code: z.ZodIssueCode.custom,
    //           message: "Option code is required",
    //         });
    //       }
    //     });
    //   }
    // }
  });
