import { Question, QuestionSchema } from "@/types/formBuilder";
import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { FieldValues, FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { InputTypeMap } from "@/constants/inputType";
import { needsOptions } from "@/lib/needsOptions";
import { DynamicOptions } from "../form-builder/DynamicOptions";
import { Switch } from "../ui";
import { ContextType, TemplateQuestion } from "@/types";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateQuestion } from "@/lib/api/form-builder";
import { showNotification } from "@/redux/slices/notificationSlice";
import { useDispatch } from "react-redux";
import { LoadingOverlay } from "../general/LoadingOverlay";
import { useTranslations } from "next-intl";

const EditQuestionModal = ({
  showModal,
  setShowModal,
  contextType,
  question,
  contextId,
}: {
  showModal: boolean;
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  contextType: ContextType;
  question: Question | TemplateQuestion;
  contextId: number;
}) => {
  const methods = useForm<z.infer<typeof QuestionSchema>>({});
  const t = useTranslations();

  const {
    register,
    formState: { errors, isSubmitted },
    setValue,
    handleSubmit,
    reset,
  } = methods;

  // Type guard to check if question is a Question type
  const isQuestion = (q: Question | TemplateQuestion): q is Question => {
    return 'position' in q;
  };

  // Helper function to safely get property from question
  const getQuestionProperty = <T,>(prop: string, defaultValue: T): T => {
    return (question as any)[prop] ?? defaultValue;
  };

  // Initialize state with default values - will be updated in useEffect
  const [isRequired, setIsRequired] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState<string>("");

  useEffect(() => {
    const result = QuestionSchema.safeParse(question);
    if (result.success) {
      // Reset form with all the data
      reset(result.data);
      setSelectedInputType(result.data.inputType || "");

      // Explicitly set questionOptions if they exist
      if (result.data.questionOptions && result.data.questionOptions.length > 0) {
        setValue("questionOptions", result.data.questionOptions);
      }
    } else {
      // Fallback for when schema parsing fails
      console.warn("Schema parsing failed, using raw question data:", result.error);

      // Manually set form values
      setValue("label", getQuestionProperty('label', ""));
      setValue("hint", getQuestionProperty('hint', ""));
      setValue("placeholder", getQuestionProperty('placeholder', ""));
      setValue("inputType", getQuestionProperty('inputType', ""));
      setSelectedInputType(getQuestionProperty('inputType', ""));

      // Handle questionOptions explicitly - check if property exists
      const questionOptions = getQuestionProperty('questionOptions', []);
      if (Array.isArray(questionOptions) && questionOptions.length > 0) {
        setValue("questionOptions", questionOptions);
      }
    }

    // Always update isRequired from the raw question data (outside schema parsing)
    setIsRequired(getQuestionProperty('isRequired', false));
  }, [question, reset, setValue]);

  useEffect(() => {
    register("inputType", { required: "Please select an input type" });
  }, [register]);

  useEffect(() => {
    setValue("inputType", selectedInputType, { shouldValidate: isSubmitted });
  }, [selectedInputType, setValue, isSubmitted]);

  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const queryKey =
    contextType === "project"
      ? ["questions"]
      : contextType === "template"
      ? ["templateQuestions"]
      : ["questionBlockQuestions"];

  const handleClose = () => {
    // Reset state to default values when closing
    setIsRequired(false);
    setSelectedInputType("");
    setShowModal(false);
  };

  const updateQuestionMutation = useMutation({
    mutationFn: updateQuestion,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey,
        exact: false,
      });
      dispatch(
        showNotification({
          message: t('questionsUpdated'),
          type: "success",
        })
      );
      handleClose();
    },
    onError: () => {
      dispatch(
        showNotification({
          message: t('questionUpdateFailed'),
          type: "error",
        })
      );
    },
  });

  const onSubmit = async (data: FieldValues) => {
    // Special handling for table input type
    if (selectedInputType === "table" || 
        ('inputType' in question && question.inputType === "table")) {
      // For table questions, we need to trigger the TableQuestionBuilder's submit
      const tableBuilder = document.querySelector(".table-question-builder");
      if (tableBuilder) {
        tableBuilder.dispatchEvent(new CustomEvent("submitTable"));
        return; // Exit early as TableQuestionBuilder handles its own submission
      }
    }

    const dataToSend = {
      label: data.label,
      isRequired,
      hint: data.hint,
      placeholder: data.placeholder,
      inputType: selectedInputType || ('inputType' in question ? question.inputType : ''),
      questionOptions: data.questionOptions,
      // Preserve the original position if it exists (for Question type)
      ...(('position' in question) && { position: question.position }),
    };
    
    updateQuestionMutation.mutate({
      id: question.id,
      contextType,
      dataToSend,
      contextId,
    });
  };

  return (
    <Modal
      isOpen={showModal}
      onClose={handleClose}
      className="w-11/12 tablet:w-4/5 desktop:w-3/5"
    >
      <h1 className="heading-text capitalize mb-4">{t('editQuestion')}</h1>

      {updateQuestionMutation.isPending && <LoadingOverlay />}
      <FormProvider {...methods}>
        <form
          className="space-y-4 max-h-[500px] overflow-y-auto p-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="label-input-group group">
            <input
              {...register("label", { required: t('questionNameRequired') })}
              className="input-field"
              placeholder={t('enterQuestion')}
            />
            {errors.label && (
              <p className="text-sm text-red-500">{`${errors.label.message}`}</p>
            )}
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="label-input-group group">
              <label htmlFor="question-type" className="label-text">
                {t('inputType')}
              </label>
              <input
                id="question-type"
                className="input-field bg-gray-100 "
                value={
                  selectedInputType && InputTypeMap[selectedInputType]
                    ? InputTypeMap[selectedInputType]
                    : "N/A"
                }
                disabled
              />
            </div>
            <div className="flex items-end">
              <div className="flex items-center space-x-2">
                <Switch
                  id="required"
                  checked={isRequired}
                  onCheckedChange={() => setIsRequired((prev) => !prev)}
                  className="data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500"
                />
                <label htmlFor="required" className="label-text">
                  {t('required')}
                </label>
              </div>
            </div>
            {errors.inputType && (
              <p className="text-sm text-red-500">{`${errors.inputType.message}`}</p>
            )}
          </div>

          <div className="label-input-group group">
            <label htmlFor="hint" className="label-text">
             {t('helpText')}
            </label>
            <textarea
              {...register("hint")}
              id="hint"
              placeholder={t('helpTextHint')}
              className="input-field resize-none"
            />
          </div>
          <div className="label-input-group group">
            <label htmlFor="placeholder" className="label-text">
              {t('placeholderText')}
            </label>
            <input
              {...register("placeholder")}
              id="placeholder"
              placeholder={t('placeholderHint')}
              className="input-field"
            />
          </div>

          {needsOptions(selectedInputType) && (
            <DynamicOptions
              contextType={contextType}
              contextId={contextId}
              currentQuestionId={question.id}
              inputType={selectedInputType}
              key={`${question.id}-${selectedInputType}`} // Add key to force re-render
            />
          )}
          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={handleClose}
              className="btn-outline"
            >
              {t('cancel')}
            </button>
            <button onClick={handleSubmit(onSubmit)} className="btn-primary">
              {t('saveEdit')}
            </button>
          </div>
        </form>
      </FormProvider>
    </Modal>
  );
};

export { EditQuestionModal };
