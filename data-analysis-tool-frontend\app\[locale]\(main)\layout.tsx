"use client";

import React, { useState, useRef, useLayoutEffect } from "react";
import Navbar from "@/components/root/Navbar";
import Sidebar from "@/components/root/Sidebar";
import { CreateProjectModal } from "@/components/modals/CreateProjectModal";
import { CreateLibraryItemModal } from "@/components/modals/CreateLibraryItemModal";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { NewProjectModal } from "@/components/modals/NewProjectModal";
import { CreateLibraryModal } from "@/components/modals/CreateLibraryModal";


export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [navbarHeight, setNavbarHeight] = useState(0);
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const navbarRef = useRef<HTMLElement | null>(null);
  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  useLayoutEffect(() => {
    const handleResize = () => {
      if (navbarRef.current) {
        setNavbarHeight(navbarRef.current.offsetHeight);
      }
    };

    const resizeObserver = new ResizeObserver(handleResize);
    if (navbarRef.current) resizeObserver.observe(navbarRef.current);

    return () => resizeObserver.disconnect();
  }, []);

  // modals visibility
  const showCreateProjectModal = useSelector(
    (state: RootState) => state.createProject.visible
  );
  const showCreateLibraryModal = useSelector(
    (state: RootState) => state.createLibrary.visible
  );
  const showCreateLibraryItemModal = useSelector(
    (state: RootState) => state.createLibraryItem.visible
  );

  const handleNewProject = () => {
    setShowNewProjectModal(true);
  };

  return (
    <div className="min-h-screen flex flex-col">
      {showCreateProjectModal && <CreateProjectModal />}
      {showCreateLibraryModal && <CreateLibraryModal />}
      {showCreateLibraryItemModal && <CreateLibraryItemModal />}
      <NewProjectModal
        isOpen={showNewProjectModal}
        onClose={() => setShowNewProjectModal(false)}
      />
      <Navbar
        toggleSidebar={toggleSidebar}
        isSidebarOpen={isSidebarOpen}
        navbarRef={navbarRef}
      />

      {/* Main area: Sidebar + Content */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          isOpen={isSidebarOpen}
          toggleSidebar={toggleSidebar}
          topOffset={navbarHeight}
          onNewProject={handleNewProject}
        />

        {/* Main Content */}
        <main
          className="flex-1 p-6 overflow-y-auto"
          style={{ height: `calc(100vh - ${navbarHeight}px)` }}
        >
          {children}
        </main>
      </div>
    </div>
  );
}
