"use client";

import { BsRocketTakeoffFill } from "react-icons/bs";
import { MdEditDocument } from "react-icons/md";
import { FaArchive } from "react-icons/fa";
import { LuLibrary } from "react-icons/lu";
import { MdFileCopy } from "react-icons/md";
import { useQuery } from "@tanstack/react-query";
import { Project } from "@/types";
import { fetchProjects } from "@/lib/api/projects";
import { useAuth } from "@/hooks/useAuth";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";


type SubItem = {
  id: number;
  label: string;
  href: string;
};

type NavItem = {
  id: number;
  icon: React.ElementType;
  label: string;
  count: number;
  href: string;
  category: "project" | "library";
  status?: "deployed" | "draft" | "archived";
  subItems?: SubItem[];
};

const useNavItems = () => {
  const { user } = useAuth();
  const locale = useLocale();
  const t = useTranslations();

  const {
    data: projectsData,
    isLoading: projectsLoading,
    isError: projectsError,
  } = useQuery<Project[]>({
    queryKey: ["projects", user?.id],
    queryFn: fetchProjects,
    enabled: !!user?.id,
  });

  // Initialize separate arrays for each status
  let deployedProjects: Project[] = [];
  let draftStatusProjects: Project[] = [];
  let archivedProjects: Project[] = [];

  if (!projectsLoading && projectsData) {
    deployedProjects = projectsData.filter((projectData) => projectData.status === "deployed");
    draftStatusProjects = projectsData.filter((projectData) => projectData.status === "draft");
    archivedProjects = projectsData.filter((projectData) => projectData.status === "archived");
  }

  const navItems: NavItem[] = [
    {
      id: 1,
      icon: BsRocketTakeoffFill,
      label: t('deployed'),
      count: deployedProjects.length || 0,
      href: `/${locale}/dashboard/deployed`,
      category: "project",
      status: "deployed",
    },
    {
      id: 2,
      icon: MdEditDocument,
      label: t('draft'),
      count: draftStatusProjects.length || 0,
      href: `/${locale}/dashboard/draft`,
      category: "project",
      status: "draft",
    },
    {
      id: 3,
      icon: FaArchive,
      label: t('archived'),
      count: archivedProjects.length || 0,
      href: `/${locale}/dashboard/archived`,
      category: "project",
      status: "archived",
    },
    {
      id: 4,
      icon: LuLibrary,
      label: t('myLibrary'),
      count: 0,
      href: `/${locale}/library`,
      category: "library",
    },
    {
      id: 5,
      icon: MdFileCopy,
      label: t('collections'),
      count: 0,
      href: `/${locale}/library/#`,
      category: "library",
    },
  ];

  return { navItems, deployedProjects, draftStatusProjects, archivedProjects };
};

export default useNavItems;
