import { InputType } from "@prisma/client";
import { prisma } from "../utils/prisma";

/**
 * TableQuestionRepository
 *
 * Handles all database operations related to table questions.
 * Responsible for:
 * - Creating, reading, updating, and deleting table questions
 * - Managing table columns and rows
 * - Handling cell values
 * - Maintaining parent-child relationships between columns
 */
class TableQuestionRepository {
  /**
   * Create a new table question with columns and rows
   *
   * @param data - Object containing table question data
   * @param data.label - Label for the table question
   * @param data.projectId - ID of the project this table belongs to
   * @param data.columns - Array of column objects with names and optional parent IDs
   * @param data.rows - Array of row objects with names
   * @returns The created table question with its columns and rows
   * @throws Error if validation fails or database operation fails
   */
  async createTableQuestion(data: {
    label: string;
    projectId: number;
    columns: { columnName: string; parentColumnId?: number }[];
    rows?: { rowsName: string }[];
  }) {
    try {
      // Validate input data
      if (!data.label || !data.label.trim()) {
        throw new Error("Table label is required");
      }

      if (!data.projectId || isNaN(data.projectId)) {
        throw new Error("Valid project ID is required");
      }

      if (
        !data.columns ||
        !Array.isArray(data.columns) ||
        data.columns.length === 0
      ) {
        throw new Error("At least one column is required");
      }

      // Rows are optional - validate only if provided
      if (data.rows && !Array.isArray(data.rows)) {
        throw new Error("Rows must be an array if provided");
      }

      // Validate column data
      for (const column of data.columns) {
        if (!column.columnName || !column.columnName.trim()) {
          throw new Error("All columns must have valid names");
        }

        // If parentColumnId is provided, make sure it's a valid number
        if (column.parentColumnId !== undefined) {
          if (isNaN(column.parentColumnId)) {
            throw new Error("Invalid parent column ID");
          }

          // Check if the parent column exists
          const parentColumn = data.columns.find(
            (_, idx) => idx + 1 === column.parentColumnId
          );
          if (!parentColumn) {
            throw new Error(
              `Parent column with position ${column.parentColumnId} not found`
            );
          }

          // Check if the parent column is already a child column
          if (parentColumn.parentColumnId !== undefined) {
            throw new Error(
              "Child columns cannot have their own children. Only parent columns can have children."
            );
          }
        }
      }

      // Count child columns per parent to enforce maximum of 2 children
      const parentChildCount = new Map<number, number>();
      data.columns.forEach((column) => {
        if (column.parentColumnId !== undefined) {
          const parentPos = column.parentColumnId;
          parentChildCount.set(
            parentPos,
            (parentChildCount.get(parentPos) || 0) + 1
          );

          if (parentChildCount.get(parentPos)! > 2) {
            const parentColumn = data.columns.find(
              (_, i) => i + 1 === parentPos
            );
            throw new Error(
              `Parent column "${
                parentColumn?.columnName || "Unknown"
              }" cannot have more than 2 child columns`
            );
          }
        }
      });

      // Validate row data if provided
      if (data.rows) {
        for (const row of data.rows) {
          if (!row.rowsName || !row.rowsName.trim()) {
            throw new Error("All rows must have valid names");
          }
        }
      }

      // Use a transaction to ensure data consistency
      return await prisma.$transaction(async (tx) => {
        // Determine the next position for the question
        const lastOrder = await tx.projectQuestionOrder.findFirst({
          where: {
            parentGroupId: data.parentGroupId ?? null,
            projectId: data.projectId,
          },
          orderBy: {
            position: "desc",
          },
        });

        const nextPosition =
          data.position ?? (lastOrder ? lastOrder.position + 1 : 1);

        // Create the question
        const question = await tx.question.create({
          data: {
            label: data.label.trim(),
            inputType: "table" as InputType,
            projectId: data.projectId,
            position: nextPosition,
            ...(data.rows &&
              data.rows.length > 0 && {
                tableRows: {
                  create: data.rows.map((row) => ({
                    rowsName: row.rowsName.trim(),
                  })),
                },
              }),
          },
          include: {
            tableRows: true,
          },
        });

        // Separate columns into parent and child columns
        const parentColumns = data.columns.filter(
          (col) => col.parentColumnId === undefined
        );
        const childColumns = data.columns.filter(
          (col) => col.parentColumnId !== undefined
        );

        console.log(
          `Creating ${parentColumns.length} parent columns and ${childColumns.length} child columns`
        );

        // Create parent columns first - supports unlimited parent columns
        const createdParentColumns = await Promise.all(
          parentColumns.map((col) =>
            tx.tableColumn.create({
              data: {
                columnName: col.columnName.trim(),
                questionId: question.id,
              },
            })
          )
        );

        // Create a simple mapping of column names to IDs for the create function
        const columnNameMapping = new Map<string, number>();
        parentColumns.forEach((col, index) => {
          columnNameMapping.set(
            col.columnName.trim(),
            createdParentColumns[index].id
          );
        });

        // Create child columns with correct parent column IDs
        if (childColumns.length > 0) {
          const createdColumnMap = new Map<number, number>();
          parentColumns.forEach((_, index) => {
            const position = data.columns.indexOf(parentColumns[index]) + 1;
            createdColumnMap.set(position, createdParentColumns[index].id);
          });

          // Process all child columns (all are direct children of parent columns)
          console.log(`Processing ${childColumns.length} child columns`);

          for (const col of childColumns) {
            const parentPosition = col.parentColumnId;
            const actualParentId =
              parentPosition !== undefined
                ? createdColumnMap.get(parentPosition)
                : undefined;

            if (actualParentId) {
              console.log(
                `Creating child column "${col.columnName}" with parent ID: ${actualParentId} (parent position: ${parentPosition})`
              );

              // Create the column
              const createdColumn = await tx.tableColumn.create({
                data: {
                  columnName: col.columnName.trim(),
                  questionId: question.id,
                  parentColumnId: actualParentId,
                },
              });

              const position = data.columns.indexOf(col) + 1;
              createdColumnMap.set(position, createdColumn.id);
            } else {
              console.warn(
                `Could not find parent ID for column "${col.columnName}" with parent position ${parentPosition}`
              );
            }
          }

          // Check for unprocessed columns
          const processedPositions = Array.from(createdColumnMap.keys());
          const allPositions = data.columns.map((_, idx) => idx + 1);
          const unprocessedPositions = allPositions.filter(
            (pos) => !processedPositions.includes(pos)
          );

          if (unprocessedPositions.length > 0) {
            console.warn(
              `${unprocessedPositions.length} columns were not processed:`,
              unprocessedPositions.map((pos) => {
                const col = data.columns[pos - 1];
                return `${col.columnName} (position: ${pos}, parent: ${col.parentColumnId})`;
              })
            );
          }
        }

        // Return the complete question with all related data
        return tx.question.findUnique({
          where: { id: question.id },
          include: {
            tableColumns: {
              include: {
                childColumns: true,
              },
            },
            tableRows: true,
          },
        });
      });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Get a table question by ID with all its columns and rows
   *
   * @param id - ID of the table question to retrieve
   * @returns The table question with its columns and rows, or null if not found
   */
  async getTableQuestionById(id: number) {
    // First get all columns for this question
    const columns = await prisma.tableColumn.findMany({
      where: {
        questionId: id,
        parentColumnId: null, // Get only parent columns
      },
      include: {
        childColumns: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // Get the question with its rows
    const question = await prisma.question.findUnique({
      where: { id },
      include: {
        tableRows: {
          orderBy: {
            id: "asc",
          },
        },
      },
    });

    if (!question) {
      return null;
    }

    // Get default values (cell values marked as default)
    const defaultValues = await prisma.columnRow.findMany({
      where: {
        column: {
          questionId: id,
        },
        isDefault: true,
      },
    });

    // Group default values by row ID
    const defaultValuesByRow = new Map();
    defaultValues.forEach((dv) => {
      if (!defaultValuesByRow.has(dv.rowsId)) {
        defaultValuesByRow.set(dv.rowsId, []);
      }
      defaultValuesByRow.get(dv.rowsId).push({
        columnId: dv.columnId,
        value: dv.value,
        code: dv.code,
      });
    });

    // Add default values to each row
    const rowsWithDefaultValues = question.tableRows.map((row) => {
      const defaultValuesForRow = defaultValuesByRow.get(row.id) || [];
      return {
        ...row,
        defaultValues: defaultValuesForRow,
      };
    });

    // Get all cell values for this question
    const cellValues = await this.getCellValues(id);

    // Combine the results
    return {
      ...question,
      tableColumns: columns,
      tableRows: rowsWithDefaultValues,
      cellValues: cellValues,
    };
  }

  /**
   * Save cell values for a table question
   *
   * @param data - Object containing cell values data
   * @param data.questionId - ID of the table question
   * @param data.cellValues - Array of cell value objects
   * @returns The created or updated cell values
   * @throws Error if the table question is not found
   */
  async saveCellValues(data: {
    questionId: number;
    cellValues: {
      columnId: number;
      rowsId: number;
      value: string;
      code?: string;
      isDefault?: boolean;
    }[];
  }) {
    // First check if the question exists
    const question = await prisma.question.findUnique({
      where: { id: data.questionId },
      include: { tableColumns: true, tableRows: true },
    });

    if (!question) {
      throw new Error("Table question not found");
    }

    // Validate IDs are within safe integer range for PostgreSQL INT4
    for (const cell of data.cellValues) {
      if (cell.columnId > 2147483647 || cell.rowsId > 2147483647) {
        throw new Error(
          `ID values too large for database: columnId=${cell.columnId}, rowId=${cell.rowsId}. Maximum allowed value is 2147483647.`
        );
      }
    }

    // Create or update cell values
    const cellUpdates = data.cellValues.map((cell) => {
      return prisma.columnRow.upsert({
        where: {
          columnId_rowsId: {
            columnId: cell.columnId,
            rowsId: cell.rowsId,
          },
        },
        update: {
          value: cell.value,
          code: cell.code,
          isDefault: cell.isDefault ?? false,
        },
        create: {
          columnId: cell.columnId,
          rowsId: cell.rowsId,
          value: cell.value,
          code: cell.code,
          isDefault: cell.isDefault ?? false,
        },
      });
    });

    return prisma.$transaction(cellUpdates);
  }

  /**
   * Get all cell values for a table question
   *
   * @param questionId - ID of the table question
   * @returns A map of cell values indexed by columnId_rowsId
   */
  async getCellValues(questionId: number) {
    const cellValues = await prisma.columnRow.findMany({
      where: {
        column: {
          questionId,
        },
      },
      include: {
        column: true,
        row: true,
      },
    });

    // Format the cell values as a map for easier access
    const cellValueMap: Record<
      string,
      { value?: string; code?: string; isDefault?: boolean }
    > = {};

    cellValues.forEach((cell) => {
      cellValueMap[`${cell.columnId}_${cell.rowsId}`] = {
        value: cell.value || "",
        code: cell.code || "",
        isDefault: cell.isDefault || false,
      };
    });

    return cellValueMap;
  }

  /**
   * Update a table question with simplified column mapping approach
   *
   * @param id - ID of the table question to update
   * @param data - Updated table question data
   * @returns The updated table question with all related data
   * @throws Error if the table question is not found or validation fails
   */
  async updateTableQuestion(
    id: number,
    data: {
      label: string;
      columns: { id?: number; columnName: string; parentColumnId?: number }[];
      rows?: {
        id?: number;
        rowsName: string;
        defaultValues?: { columnId: number; value: string; code?: string }[];
      }[];
      projectId: number;
    }
  ) {
    try {
      console.log(`Starting simplified update for table question ${id}`);

      // Basic validation
      if (!data.label || !data.label.trim()) {
        throw new Error("Table label is required");
      }

      if (!data.columns || data.columns.length === 0) {
        throw new Error("At least one column is required");
      }

      // Validate column names and parent-child relationships
      for (const column of data.columns) {
        if (!column.columnName || !column.columnName.trim()) {
          throw new Error("All columns must have valid names");
        }

        // If parentColumnId is provided, validate the parent-child relationship
        if (column.parentColumnId !== undefined) {
          // Check if the parent column exists by ID
          // For existing columns, parentColumnId should be the actual database ID
          // For new columns, it might be a position-based index
          let parentColumn = data.columns.find(
            (col) => col.id === column.parentColumnId
          );

          // If we can't find by ID, try to find by position (1-based index)
          if (
            !parentColumn &&
            column.parentColumnId > 0 &&
            column.parentColumnId <= data.columns.length
          ) {
            // Adjust for 1-based indexing
            parentColumn = data.columns[column.parentColumnId - 1];
            console.log(
              `Found parent column by position: ${column.parentColumnId}, name: ${parentColumn?.columnName}`
            );
          }

          if (!parentColumn) {
            throw new Error(
              `Parent column with ID/position ${column.parentColumnId} not found`
            );
          }

          // Check if the parent column is already a child column
          // We don't want children to have their own children (no grandchildren)
          if (parentColumn.parentColumnId !== undefined) {
            throw new Error(
              "Child columns cannot have their own children. Only parent columns can have children."
            );
          }
        }
      }

      // Count child columns per parent to enforce maximum of 2 children per parent
      const parentChildCount = new Map<number, number>();

      // Count how many children each parent has
      data.columns.forEach((column) => {
        if (column.parentColumnId !== undefined) {
          const parentId = column.parentColumnId;
          parentChildCount.set(
            parentId,
            (parentChildCount.get(parentId) || 0) + 1
          );

          // Check if any parent has more than 2 children
          if (parentChildCount.get(parentId)! > 2) {
            const parentColumn = data.columns.find(
              (col) => col.id === parentId
            );
            throw new Error(
              `Parent column "${
                parentColumn?.columnName || "Unknown"
              }" cannot have more than 2 child columns`
            );
          }
        }
      });

      // Validate row data if rows are provided
      if (data.rows) {
        for (const row of data.rows) {
          if (!row.rowsName || !row.rowsName.trim()) {
            throw new Error("All rows must have valid names");
          }
        }
      }

      // Use a simplified transaction approach
      return await prisma.$transaction(async (tx) => {
        console.log(`Updating question ${id} with simplified approach`);

        // Update the question label and projectId
        await tx.question.update({
          where: { id },
          data: {
            label: data.label.trim(),
            projectId: data.projectId,
          },
        });

        // Get existing data for reference
        const existingColumns = await tx.tableColumn.findMany({
          where: { questionId: id },
        });

        const existingRows = await tx.tableRow.findMany({
          where: { questionId: id },
        });

        // Handle rows first (simpler) - only if rows are provided
        if (data.rows) {
          // 1. Update existing rows
          const rowsToUpdate = data.rows
            .filter((row) => row.id !== undefined)
            .map((row) => ({
              id: row.id!,
              rowsName: row.rowsName.trim(),
            }));

          for (const row of rowsToUpdate) {
            await tx.tableRow.update({
              where: { id: row.id },
              data: { rowsName: row.rowsName },
            });
          }

          // 2. Add new rows
          const rowsToAdd = data.rows
            .filter((row) => row.id === undefined)
            .map((row) => ({
              rowsName: row.rowsName.trim(),
              questionId: id,
            }));

          if (rowsToAdd.length > 0) {
            await tx.tableRow.createMany({
              data: rowsToAdd,
            });
          }

          // 3. Delete rows that are no longer needed
          const rowIdsToKeep = data.rows
            .filter((row) => row.id !== undefined)
            .map((row) => row.id!);
          const rowIdsToDelete = existingRows
            .filter((row) => !rowIdsToKeep.includes(row.id))
            .map((row) => row.id);

          if (rowIdsToDelete.length > 0) {
            await tx.tableRow.deleteMany({
              where: {
                id: {
                  in: rowIdsToDelete,
                },
              },
            });
          }
        } else {
          // If no rows provided, delete all existing rows
          if (existingRows.length > 0) {
            await tx.tableRow.deleteMany({
              where: { questionId: id },
            });
          }
        }

        // Handle columns (more complex due to parent-child relationships)
        // Log the column structure we're about to create
        console.log(
          "Column structure to create:",
          data.columns.map((col) => ({
            name: col.columnName,
            id: col.id,
            parentId: col.parentColumnId,
          }))
        );

        // 1. Delete all existing columns and recreate them to avoid complex parent-child relationship updates
        // This is safer than trying to update in place with complex hierarchies
        await tx.tableColumn.deleteMany({
          where: { questionId: id },
        });

        console.log(`Deleted existing columns for question ${id}`);

        // STEP 2: Create new columns with simplified approach
        // Create parent columns first
        const parentColumns = data.columns.filter(
          (col) => col.parentColumnId === undefined
        );

        // Create child columns
        const childColumns = data.columns.filter(
          (col) => col.parentColumnId !== undefined
        );

        console.log(
          `Processing ${parentColumns.length} parent columns and ${childColumns.length} child columns`
        );

        // Create parent columns first - supports unlimited parent columns
        const createdParentColumns = await Promise.all(
          parentColumns.map((col) =>
            tx.tableColumn.create({
              data: {
                columnName: col.columnName.trim(),
                questionId: id,
              },
            })
          )
        );

        console.log(`Created ${createdParentColumns.length} parent columns`);

        // Now create child columns with the correct parent column IDs
        if (childColumns.length > 0) {
          // Create a map to store the created column IDs
          // This will map from the original column ID to the created column ID
          const createdColumnMap = new Map<number, number>();

          // Add parent columns to the map
          parentColumns.forEach((col, index) => {
            // If the column has an ID, map it to the created column ID
            if (col.id !== undefined) {
              createdColumnMap.set(col.id, createdParentColumns[index].id);
              console.log(
                `Mapped parent column with ID ${col.id} to created ID ${createdParentColumns[index].id}`
              );
            }

            // Also map parent columns by their position in the array (for new columns without IDs)
            const position = data.columns.indexOf(col) + 1; // 1-based index
            createdColumnMap.set(position, createdParentColumns[index].id);
            console.log(
              `Mapped parent column at position ${position} to created ID ${createdParentColumns[index].id}`
            );
          });

          // Process all child columns (all are direct children of parent columns)
          console.log(`Processing ${childColumns.length} child columns`);

          for (const col of childColumns) {
            // Get the parent column's ID
            const parentId = col.parentColumnId;

            // Get the actual ID of the parent column that was created
            let actualParentId: number | undefined = undefined;

            if (parentId !== undefined) {
              // First try to get the parent ID from the map using the database ID
              actualParentId = createdColumnMap.get(parentId);

              // If that fails, try to get it using the position (for new columns)
              if (actualParentId === undefined) {
                // Find the parent column's position in the array
                const parentPosition =
                  data.columns.findIndex(
                    (c) => c === data.columns.find((c) => c.id === parentId)
                  ) + 1; // 1-based index

                if (parentPosition > 0) {
                  actualParentId = createdColumnMap.get(parentPosition);
                  console.log(
                    `Using position-based mapping for parent: position ${parentPosition} maps to ID ${actualParentId}`
                  );
                }
              }
            }

            if (actualParentId) {
              console.log(
                `Creating child column "${col.columnName}" with parent ID: ${actualParentId} (original parent ID: ${parentId})`
              );

              // Create the column
              const createdColumn = await tx.tableColumn.create({
                data: {
                  columnName: col.columnName.trim(),
                  questionId: id,
                  parentColumnId: actualParentId,
                },
              });

              // Add the created column to the map
              // If the column has an ID, map it to the created column ID
              if (col.id !== undefined) {
                createdColumnMap.set(col.id, createdColumn.id);
                console.log(
                  `Mapped child column with ID ${col.id} to created ID ${createdColumn.id}`
                );
              }

              // Also map by position
              const position = data.columns.indexOf(col) + 1; // 1-based index
              createdColumnMap.set(position, createdColumn.id);
              console.log(
                `Mapped child column at position ${position} to created ID ${createdColumn.id}`
              );
            } else {
              console.warn(
                `Could not find parent ID for column "${col.columnName}" with parent ID ${parentId}`
              );
            }
          }

          // STEP 5: Clean up cell values that are no longer needed
          // Get all current cell values for this question
          const currentCellValues = await tx.columnRow.findMany({
            where: {
              column: {
                questionId: id,
              },
            },
          });

          // Create a set of expected cell values based on current columns and rows
          const expectedCellValues = new Set<string>();
          const currentColumns = await tx.tableColumn.findMany({
            where: { questionId: id },
          });
          const currentRows = await tx.tableRow.findMany({
            where: { questionId: id },
          });

          // Generate expected keys for all valid column-row combinations
          currentColumns.forEach((col) => {
            currentRows.forEach((row) => {
              expectedCellValues.add(`${col.id}_${row.id}`);
            });
          });

          // Delete cell values that are not in the expected set
          const cellValuesToDelete = currentCellValues.filter((cellValue) => {
            const key = `${cellValue.columnId}_${cellValue.rowsId}`;
            return !expectedCellValues.has(key);
          });

          if (cellValuesToDelete.length > 0) {
            console.log(
              `Deleting ${cellValuesToDelete.length} obsolete cell values`
            );

            for (const cellValue of cellValuesToDelete) {
              try {
                await tx.columnRow.delete({
                  where: {
                    columnId_rowsId: {
                      columnId: cellValue.columnId,
                      rowsId: cellValue.rowsId,
                    },
                  },
                });
                console.log(
                  `Deleted cell value for column ${cellValue.columnId}, row ${cellValue.rowsId}`
                );
              } catch (error) {
                console.warn(`Failed to delete cell value:`, error);
              }
            }
          }

          // Delete rows that are no longer needed (only if rows are provided)
          if (data.rows) {
            const rowIdsToKeep = data.rows
              .filter((row) => row.id !== undefined)
              .map((row) => row.id!);
            const rowIdsToDelete = existingRows
              .filter((row) => !rowIdsToKeep.includes(row.id))
              .map((row) => row.id);

            if (rowIdsToDelete.length > 0) {
              await tx.tableRow.deleteMany({
                where: { id: { in: rowIdsToDelete } },
              });
            }
          }
        } else {
          // If no rows provided, delete all existing rows and cell values
          await tx.tableRow.deleteMany({
            where: { questionId: id },
          });

          // Also delete all cell values since there are no rows
          await tx.columnRow.deleteMany({
            where: {
              column: {
                questionId: id,
              },
            },
          });
          console.log("Deleted all cell values since no rows were provided");
        }

        console.log(`Successfully updated table question ${id}`);

        // Get the updated question with all related data
        const updatedQuestion = await tx.question.findUnique({
          where: { id },
          include: {
            tableColumns: {
              include: {
                childColumns: true,
              },
            },
            tableRows: true,
          },
        });

        if (!updatedQuestion) {
          throw new Error("Failed to retrieve updated question");
        }

        // Get the updated cell values
        const cellValues = await this.getCellValues(id);

        // Get default values (cell values marked as default)
        const defaultValues = await tx.columnRow.findMany({
          where: {
            column: {
              questionId: id,
            },
            isDefault: true,
          },
        });

        // Group default values by row ID
        const defaultValuesByRow = new Map();
        defaultValues.forEach((dv) => {
          if (!defaultValuesByRow.has(dv.rowsId)) {
            defaultValuesByRow.set(dv.rowsId, []);
          }
          defaultValuesByRow.get(dv.rowsId).push({
            columnId: dv.columnId,
            value: dv.value,
            code: dv.code,
          });
        });

        // Add default values to each row
        const rowsWithDefaultValues = updatedQuestion.tableRows.map((row) => {
          const defaultValuesForRow = defaultValuesByRow.get(row.id) || [];
          return {
            ...row,
            defaultValues: defaultValuesForRow,
          };
        });

        // Return the complete updated question with cell values
        return {
          ...updatedQuestion,
          tableRows: rowsWithDefaultValues,
          cellValues: cellValues,
        };
      });
    } catch (error: any) {
      throw error;
    }
  }

  /**
   * Delete a table question and all related data
   *
   * @param id - ID of the table question to delete
   * @returns The deleted table question
   * @throws Error if the table question is not found
   */
  async deleteTableQuestion(id: number) {
    // This will cascade delete all related columns, rows, and cell values
    return prisma.question.delete({
      where: { id },
    });
  }

  /**
   * Get all table questions for a project
   *
   * @param projectId - ID of the project
   * @returns Array of table questions with their columns and rows
   */
  async getTableQuestionsByProjectId(projectId: number) {
    // First get all table questions for this project
    const questions = await prisma.question.findMany({
      where: {
        projectId,
        inputType: "table",
        tableColumns: {
          some: {},
        },
      },
      include: {
        tableRows: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // If no questions found, return empty array
    if (questions.length === 0) {
      return [];
    }

    // Get all question IDs
    const questionIds = questions.map((q) => q.id);

    // Get all parent columns for these questions with their children
    const allParentColumns = await prisma.tableColumn.findMany({
      where: {
        questionId: { in: questionIds },
        parentColumnId: null, // Only get parent columns
      },
      include: {
        childColumns: {
          orderBy: {
            id: "asc",
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    // Group columns by question ID
    const columnsByQuestionId = new Map<number, any[]>();
    allParentColumns.forEach((column) => {
      const questionId = column.questionId;
      if (!columnsByQuestionId.has(questionId)) {
        columnsByQuestionId.set(questionId, []);
      }
      columnsByQuestionId.get(questionId)!.push(column);
    });

    // Combine the results
    return questions.map((question) => ({
      ...question,
      tableColumns: columnsByQuestionId.get(question.id) || [],
    }));
  }
}

export default new TableQuestionRepository();
