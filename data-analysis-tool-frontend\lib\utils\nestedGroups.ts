import { Question, QuestionGroup } from "@/types/formBuilder";

/**
 * Build nested group structure from flat array of groups and questions
 * This function creates a hierarchical structure where subgroups are nested within their parent groups
 */
export const buildNestedGroups = (
  groups: QuestionGroup[],
  questions: Question[]
): QuestionGroup[] => {
  const groupMap = new Map<number, QuestionGroup>();
  
  // Create a map of all groups with their subGroups and questions initialized
  groups.forEach(group => {
    // Get questions for this group
    const groupQuestions = questions
      .filter((q) => q.questionGroupId === group.id)
      .sort((a, b) => a.position - b.position);
    
    groupMap.set(group.id, { 
      ...group, 
      subGroups: [],
      question: groupQuestions
    });
  });

  // Build the nested structure
  const topLevelGroups: QuestionGroup[] = [];
  groups.forEach(group => {
    const groupWithSubGroups = groupMap.get(group.id)!;
    
    if (group.parentGroupId) {
      // This is a child group, add it to its parent's subGroups
      const parentGroup = groupMap.get(group.parentGroupId);
      if (parentGroup) {
        parentGroup.subGroups = parentGroup.subGroups || [];
        parentGroup.subGroups.push(groupWithSubGroups);
      }
    } else {
      // This is a top-level group
      topLevelGroups.push(groupWithSubGroups);
    }
  });

  return topLevelGroups;
};

/**
 * Create unified form items (groups and ungrouped questions) for rendering
 * This maintains the same ordering logic as the form builder
 */
export const createUnifiedFormItems = (
  nestedGroups: QuestionGroup[],
  ungroupedQuestions: Question[]
): Array<{
  type: 'group' | 'question';
  data: QuestionGroup | Question;
  order: number;
  originalPosition?: number;
}> => {
  const items: Array<{
    type: 'group' | 'question';
    data: QuestionGroup | Question;
    order: number;
    originalPosition?: number;
  }> = [];

  // Add question groups
  nestedGroups.forEach((group: QuestionGroup) => {
    // For groups, find the minimum position of questions in the group (including subgroups)
    const getAllGroupQuestions = (g: QuestionGroup): Question[] => {
      const directQuestions = g.question || [];
      const subGroupQuestions = (g.subGroups || []).flatMap(getAllGroupQuestions);
      return [...directQuestions, ...subGroupQuestions];
    };

    const allGroupQuestions = getAllGroupQuestions(group);
    const minQuestionPosition = allGroupQuestions.length > 0
      ? Math.min(...allGroupQuestions.map(q => q.position))
      : group.order;

    items.push({
      type: 'group',
      data: group,
      order: minQuestionPosition,
      originalPosition: minQuestionPosition
    });
  });

  // Add ungrouped questions
  ungroupedQuestions.forEach((question: Question) => {
    items.push({
      type: 'question',
      data: question,
      order: question.position,
      originalPosition: question.position
    });
  });

  // Sort by order/position with secondary sort for consistency
  return items.sort((a, b) => {
    if (a.order === b.order) {
      return (a.originalPosition || a.order) - (b.originalPosition || b.order);
    }
    return a.order - b.order;
  });
};

/**
 * Get ungrouped questions (questions not belonging to any group)
 */
export const getUngroupedQuestions = (questions: Question[]): Question[] => {
  return questions.filter(
    (q) => q.questionGroupId === null || q.questionGroupId === undefined
  );
};

/**
 * Get all group IDs from nested group structure (including subgroups)
 */
export const getAllGroupIds = (groups: QuestionGroup[]): number[] => {
  const ids: number[] = [];
  groups.forEach(group => {
    ids.push(group.id);
    if (group.subGroups && group.subGroups.length > 0) {
      ids.push(...getAllGroupIds(group.subGroups));
    }
  });
  return ids;
};

/**
 * Initialize expansion state for all groups (including nested ones)
 */
export const initializeGroupExpansionState = (
  nestedGroups: QuestionGroup[],
  defaultExpanded: boolean = true
): Record<number, boolean> => {
  const initialExpandedState: Record<number, boolean> = {};
  const allGroupIds = getAllGroupIds(nestedGroups);
  allGroupIds.forEach((groupId) => {
    initialExpandedState[groupId] = defaultExpanded;
  });
  return initialExpandedState;
};
