"use client";

import { QuestionBlockNavbar } from "@/components/question-block/questionBlockNavbar";
import Link from "next/link";
import React from "react";
import { ArrowLeft } from "lucide-react";
import { useTranslations } from "next-intl";

const QuestionBlockLayout = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations();
  return (
    <div className="section flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <h1 className="heading-text capitalize">{t('questionBlock')}</h1>
        <Link href="/library" className="flex items-center gap-2">
          <ArrowLeft size={16} />
          {t('backToLibrary')}
        </Link>
      </div>
      <QuestionBlockNavbar />
      <div className="px-8">{children}</div>
    </div>
  );
};

export default QuestionBlockLayout;
